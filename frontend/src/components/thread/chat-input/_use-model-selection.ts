'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';

export const STORAGE_KEY_MODEL = 'suna-preferred-model';
export const SONNET4_MODEL_ID = 'claude-4-sonnet';
export const GPT41_MODEL_ID = 'gpt-4.1';

export type SubscriptionStatus = 'no_subscription' | 'active';

export interface ModelOption {
  id: string;
  label: string;
  description?: string;
}

export const MODEL_OPTIONS: ModelOption[] = [
  {
    id: SONNET4_MODEL_ID,
    label: 'AL1',
    description: 'Advanced Language model - Claude 4 Sonnet for enhanced performance',
  },
  {
    id: GPT41_MODEL_ID,
    label: 'Normal',
    description: 'Standard mode - GPT-4.1 for quick and reliable responses',
  },
];

export const useModelSelection = () => {
  const [selectedModel, setSelectedModelState] = useState<string>(GPT41_MODEL_ID);

  // Load saved model preference on mount
  useEffect(() => {
    const savedModel = localStorage.getItem(STORAGE_KEY_MODEL);
    if (savedModel && MODEL_OPTIONS.some(option => option.id === savedModel)) {
      setSelectedModelState(savedModel);
    }
  }, []);

  const setSelectedModel = (modelId: string) => {
    if (MODEL_OPTIONS.some(option => option.id === modelId)) {
      const modelOption = MODEL_OPTIONS.find(option => option.id === modelId);
      const previousModel = MODEL_OPTIONS.find(option => option.id === selectedModel);

      setSelectedModelState(modelId);
      localStorage.setItem(STORAGE_KEY_MODEL, modelId);

      // Show toast notification for model change
      if (modelOption && previousModel && modelId !== selectedModel) {
        const modeName = modelOption.label;
        const modeDescription = modelId === SONNET4_MODEL_ID
          ? 'Advanced Language model active'
          : 'Standard mode active';

        toast.success(`Switched to ${modeName} mode`, {
          description: modeDescription,
          duration: 3000,
        });
      }
    }
  };

  return {
    selectedModel,
    setSelectedModel,
    subscriptionStatus: 'no_subscription' as SubscriptionStatus,
    availableModels: MODEL_OPTIONS,
    allModels: MODEL_OPTIONS,
    canAccessModel: () => true,
    isSubscriptionRequired: () => false,
  };
};
