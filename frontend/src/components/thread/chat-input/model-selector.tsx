'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { SONNET4_MODEL_ID, GPT41_MODEL_ID } from './_use-model-selection';

interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  modelOptions: any[];
  canAccessModel: (modelId: string) => boolean;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModel,
  onModelChange,
  canAccessModel,
}) => {
  // Determine if we're in AL1 mode (Claude Sonnet) or normal mode (GPT 4.1)
  const isAL1Mode = selectedModel === SONNET4_MODEL_ID;

  const handleToggle = () => {
    const newModel = isAL1Mode ? GPT41_MODEL_ID : SONNET4_MODEL_ID;
    if (canAccessModel(newModel)) {
      onModelChange(newModel);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <button
        onClick={handleToggle}
        className={cn(
          "relative inline-flex h-5 w-9 items-center rounded-full transition-colors duration-200 ease-in-out focus:outline-none",
          isAL1Mode
            ? "bg-blue-600"
            : "bg-gray-300 dark:bg-gray-600"
        )}
        type="button"
        role="switch"
        aria-checked={isAL1Mode}
        aria-label={`Switch to ${isAL1Mode ? 'normal' : 'AL1'} mode`}
      >
        {/* Toggle circle */}
        <span
          className={cn(
            "inline-block h-3 w-3 transform rounded-full bg-white transition-transform duration-200 ease-in-out",
            isAL1Mode ? "translate-x-5" : "translate-x-1"
          )}
        />
      </button>

      {/* Mode label */}
      <span className={cn(
        "text-xs text-muted-foreground",
        isAL1Mode ? "font-bold" : "font-normal"
      )}>
        AL1
      </span>
    </div>
  );
};
