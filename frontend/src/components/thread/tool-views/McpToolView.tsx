import React from 'react';
import { ToolViewProps } from './types';
import { formatTimestamp, getToolTitle, parseToolOutput } from './utils';
import { CircleDashed, CheckCircle, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Markdown } from '@/components/ui/markdown';
import { McpIcon } from '@/components/ui/mcp-icon';
import { useState, useEffect } from 'react';

// Map of Integration tool names to their display titles
const MCP_TOOL_TITLES = {
  'gmail-mcp': 'Gmail Integration',
  'notion-mcp': 'Notion Integration',
  'google-drive-mcp': 'Google Drive Integration',
  'slack-mcp': 'Slack Integration',
  'apollo-mcp': 'Apollo Integration'
};

export function McpToolView({
  name = 'mcp-tool',
  assistantContent,
  toolContent,
  isSuccess = true,
  isStreaming = false,
  assistantTimestamp,
  toolTimestamp,
}: ToolViewProps) {
  // Use the consistent Integration icon component
  const McpCdnIcon = (props: any) => {
    return <McpIcon size={props.size || 20} className={props.className} />;
  };

  // Get the tool title from the mapping or use a formatted version of the name
  const toolTitle = MCP_TOOL_TITLES[name] || getToolTitle(name);

  // Parse both assistant content and tool content
  const parsedAssistant = React.useMemo(() => assistantContent ? parseToolOutput(assistantContent) : null, [assistantContent]);
  const parsedTool = React.useMemo(() => toolContent ? parseToolOutput(toolContent) : null, [toolContent]);

  // Helper to render tool call with icon
  function renderToolCall(content: string) {
    // Extract tool name and content from XML tags
    const toolCallMatch = content.match(/<([^>]+)>([\s\S]*?)<\/\1>/);
    if (!toolCallMatch) return content;

    const [fullMatch, toolName, toolContent] = toolCallMatch;
    const displayName = getToolTitle(toolName);

    return (
      <div className="rounded p-2 border bg-blue-50 dark:bg-blue-950/30 border-blue-100 dark:border-blue-900">
        <div className="flex items-start gap-2">
          <div className="mt-0.5">
            <McpCdnIcon size={16} />
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-xs font-medium mb-1 text-blue-700 dark:text-blue-300">
              {displayName}
            </div>
            <div className="text-xs whitespace-pre-wrap break-words text-blue-600 dark:text-blue-400">
              {toolContent.trim()}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Helper to render parsed output
  function renderParsedOutput(parsed: any, isInput = false) {
    if (!parsed) return null;
    if (parsed.type === 'error') {
      return (
        <div className="rounded bg-zinc-50 dark:bg-zinc-900 p-2 border border-zinc-100 dark:border-zinc-800">
          <div className="mb-2 flex items-center gap-2">
            <span className="px-2 py-0.5 rounded text-xs font-semibold bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300">Unrecognized format</span>
            <Markdown className="text-xs text-zinc-800 dark:text-zinc-300">{String(parsed.value)}</Markdown>
          </div>
        </div>
      );
    }

    // For input, check for tool calls
    if (isInput && typeof parsed.value === 'string') {
      const hasToolCall = /<[^>]+>[^<]*<\/[^>]+>/.test(parsed.value);
      if (hasToolCall) {
        return renderToolCall(parsed.value);
      }
    }

    // Default container for all other content types
    return (
      <div className="rounded bg-zinc-50 dark:bg-zinc-900 p-2 border border-zinc-100 dark:border-zinc-800">
        {parsed.type === 'json' ? (
          Array.isArray(parsed.value) ? (
            <ul className="list-disc pl-5 space-y-2">
              {parsed.value.map((item: any, idx: number) => (
                <li key={idx}>
                  <Markdown className="text-xs text-zinc-800 dark:text-zinc-300">{JSON.stringify(item, null, 2)}</Markdown>
                </li>
              ))}
            </ul>
          ) : typeof parsed.value === 'object' && parsed.value !== null ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {Object.entries(parsed.value).map(([key, val]) => (
                <div key={key}>
                  <div className="text-xs font-semibold text-zinc-500 dark:text-zinc-400 mb-1">{key}</div>
                  <Markdown className="text-xs text-zinc-800 dark:text-zinc-300">{typeof val === 'string' ? val : JSON.stringify(val, null, 2)}</Markdown>
                </div>
              ))}
            </div>
          ) : (
            <Markdown className="text-xs text-zinc-800 dark:text-zinc-300">{String(parsed.value)}</Markdown>
          )
        ) : (
          <Markdown className="text-xs text-zinc-800 dark:text-zinc-300">{String(parsed.value)}</Markdown>
        )}
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 p-4 overflow-auto">
        <div className="border border-zinc-200 dark:border-zinc-800 rounded-md overflow-hidden h-full flex flex-col">
          {/* Header */}
          <div className="flex items-center p-2 bg-zinc-100 dark:bg-zinc-900 justify-between border-b border-zinc-200 dark:border-zinc-800">
            <div className="flex items-center">
              <McpCdnIcon className="h-4 w-4 mr-2" />
              <span className="text-xs font-medium text-zinc-700 dark:text-zinc-300">
                {toolTitle}
              </span>
            </div>
            {!isStreaming && (
              <span
                className={cn(
                  'text-xs flex items-center',
                  isSuccess
                    ? 'text-emerald-600 dark:text-emerald-400'
                    : 'text-red-600 dark:text-red-400',
                )}
              >
                <span className="h-1.5 w-1.5 rounded-full mr-1.5 bg-current"></span>
                {isSuccess ? 'Success' : 'Failed'}
              </span>
            )}
          </div>

          <div className="flex-1 overflow-auto bg-white dark:bg-zinc-950">
            {/* Assistant Input */}
            {assistantContent && !isStreaming && (
              <div className="px-3 py-2 border-b border-zinc-200 dark:border-zinc-800">
                <div className="text-xs font-medium text-zinc-500 dark:text-zinc-400 mb-1">Input</div>
                {renderParsedOutput(parsedAssistant, true)}
              </div>
            )}

            {/* Tool Result / Streaming State */}
            {isStreaming ? (
              <div className="px-3 py-4 flex flex-col items-center justify-center">
                <CircleDashed className="h-8 w-8 mb-3 text-blue-500 animate-spin" />
                <p className="text-base font-medium text-zinc-700 dark:text-zinc-200">Running integration tool...</p>
                <p className="text-xs mt-1 text-zinc-500 dark:text-zinc-400">This may take a few moments</p>
              </div>
            ) : parsedTool ? (
              <div className="px-3 py-2">
                <div className="text-xs font-medium text-zinc-500 dark:text-zinc-400 mb-1">Output</div>
                <div className="rounded bg-zinc-50 dark:bg-zinc-900 p-2 border border-zinc-100 dark:border-zinc-800">
                  {renderParsedOutput(parsedTool)}
                </div>
              </div>
            ) : (
              <div className="px-3 py-4 flex flex-col items-center justify-center">
                <McpCdnIcon />
                <p className="text-sm font-medium text-zinc-700 dark:text-zinc-300">No result yet</p>
                <p className="text-xs mt-1 text-zinc-500 dark:text-zinc-400">Waiting for integration tool output</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-zinc-200 dark:border-zinc-800">
        <div className="flex items-center justify-between text-xs text-zinc-500 dark:text-zinc-400">
          {!isStreaming && (
            <div className="flex items-center gap-2">
              {isSuccess ? (
                <CheckCircle className="h-3.5 w-3.5 text-emerald-500" />
              ) : (
                <AlertTriangle className="h-3.5 w-3.5 text-red-500" />
              )}
              <span>
                {isSuccess ? `${toolTitle} completed successfully` : `${toolTitle} failed`}
              </span>
            </div>
          )}

          {isStreaming && (
            <div className="flex items-center gap-2">
              <CircleDashed className="h-3.5 w-3.5 text-blue-500 animate-spin" />
              <span>Executing {toolTitle.toLowerCase()}...</span>
            </div>
          )}

          <div className="text-xs">
            {toolTimestamp && !isStreaming
              ? formatTimestamp(toolTimestamp)
              : assistantTimestamp
                ? formatTimestamp(assistantTimestamp)
                : ''}
          </div>
        </div>
      </div>
    </div>
  );
}
