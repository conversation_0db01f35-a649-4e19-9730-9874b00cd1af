"""
Universal Composio XML Tool

This module provides a single, universal XML tool class that works for any Composio service.
It dynamically handles parameter parsing and execution for all Composio integrations.
"""

import logging
import json
from typing import Dict, Any, List, Optional
from agentpress.tool import Tool

logger = logging.getLogger(__name__)


class UniversalComposioXMLTool(Tool):
    """
    Universal XML tool that works for any Composio service.

    This single class replaces the need for service-specific tool classes.
    It dynamically handles:
    - Parameter parsing based on the generated XML schema
    - Action validation against available actions
    - Execution via ComposioXMLService
    - Result formatting
    """

    def __init__(
        self,
        service_name: str,
        user_id: str,
        available_actions: List[str],
        composio_service,
        connection_id: Optional[str] = None,
        xml_schema: Optional[str] = None,
        parameter_mappings: Optional[List[Dict[str, Any]]] = None,
    ):
        """
        Initialize the universal Composio XML tool.

        Args:
            service_name: Name of the service (e.g., "gmail", "notion")
            user_id: User ID for entity isolation
            available_actions: List of available action names for this service
            composio_service: Instance of ComposioXMLService
            connection_id: Composio connection ID for this service
            xml_schema: Generated XML schema string
            parameter_mappings: Parameter mappings for XML parsing
        """
        self.service_name = service_name
        self.user_id = user_id
        self.available_actions = available_actions
        self.composio_service = composio_service
        self.connection_id = connection_id
        self.xml_schema_content = xml_schema
        self.parameter_mappings = parameter_mappings or []

        # Generate tool name and XML tag
        self.tool_name = f"{service_name}_tool"
        self.xml_tag = f"{service_name}-action"

        super().__init__()

    @property
    def name(self) -> str:
        """Return the tool name."""
        return self.tool_name

    @property
    def xml_schema(self) -> str:
        """Return the XML schema for this tool."""
        if self.xml_schema_content:
            return self.xml_schema_content

        # Fallback schema if none provided
        return f"""
<{self.xml_tag} action="ACTION_NAME" [parameters]>
  [optional_content]
</{self.xml_tag}>

{self.service_name.title()} Actions:
{chr(10).join(f"- {action}" for action in self.available_actions[:10])}
{f"... and {len(self.available_actions) - 10} more actions" if len(self.available_actions) > 10 else ""}

Example:
<{self.xml_tag} action="{self.available_actions[0] if self.available_actions else 'ACTION_NAME'}">
  Optional content
</{self.xml_tag}>
"""

    async def execute(self, **kwargs) -> str:
        """
        Execute the XML tool by parsing parameters and calling Composio.

        This method:
        1. Extracts the 'action' parameter to determine which Composio action to call
        2. Maps user-friendly action names to actual Composio action names
        3. Parses other parameters from XML attributes and content
        4. Calls the ComposioXMLService to execute the action
        5. Returns formatted results

        Args:
            **kwargs: XML tool parameters including 'action' and other parameters

        Returns:
            Formatted execution result as string
        """
        logger.info(
            f"🎯 Universal {self.service_name} XML tool execute called for user {self.user_id}"
        )
        logger.debug(f"📝 Received kwargs: {kwargs}")

        try:
            # Extract action parameter
            action = kwargs.get("action")
            logger.debug(f"🔍 Extracted action: {action}")

            if not action:
                error_msg = "Missing required 'action' parameter"
                logger.error(f"❌ {error_msg}")
                return self._format_error(error_msg)

            # Map user-friendly action names to actual Composio action names
            mapped_action = self._map_action_name(action)
            logger.debug(f"🔄 Mapped action '{action}' to '{mapped_action}'")

            # Validate action is available (try both original and mapped)
            logger.debug(
                f"🔍 Validating action '{action}' (mapped: '{mapped_action}') against available actions: {self.available_actions}"
            )

            final_action = None
            if action in self.available_actions:
                final_action = action
            elif mapped_action in self.available_actions:
                final_action = mapped_action
            else:
                available_str = ", ".join(self.available_actions[:5])
                if len(self.available_actions) > 5:
                    available_str += f" (and {len(self.available_actions) - 5} more)"
                error_msg = (
                    f"Action '{action}' not available for {self.service_name}. "
                    f"Available actions: {available_str}"
                )
                logger.error(f"❌ {error_msg}")
                return self._format_error(error_msg)

            logger.debug(f"✅ Using final action: '{final_action}'")

            # Parse parameters using dynamic mapping
            parameters = self._parse_parameters(kwargs)
            logger.debug(f"📋 Parsed parameters: {parameters}")

            logger.info(
                f"🚀 Executing {self.service_name} action '{final_action}' for user {self.user_id}"
            )

            # Execute via ComposioXMLService
            logger.debug(f"🔧 Calling ComposioXMLService.execute_xml_tool")
            result = await self.composio_service.execute_xml_tool(
                user_id=self.user_id,
                service_name=self.service_name,
                action=final_action,
                parameters=parameters,
            )

            logger.debug(f"📤 Got result from ComposioXMLService: {result}")

            # Format and return result
            if result.get("success"):
                logger.info(
                    f"🎉 Successfully executed {self.service_name} action '{final_action}'"
                )
                return self._format_success(final_action, result.get("data", {}))
            else:
                error_msg = result.get("error", "Unknown error")
                logger.error(f"❌ Execution failed: {error_msg}")
                return self._format_error(error_msg)

        except Exception as e:
            logger.error(
                f"💥 Error executing {self.service_name} tool: {e}", exc_info=True
            )
            return self._format_error(f"Execution error: {str(e)}")

    def _parse_parameters(self, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse parameters from kwargs using dynamic parameter mappings.

        Args:
            kwargs: Raw parameters from XML parsing

        Returns:
            Parsed parameters dictionary
        """
        parameters = {}

        for k, v in kwargs.items():
            if k == "action":
                continue  # Skip action parameter
            elif k == "content" and v:
                # Handle content parameter - map to service-specific parameter names
                content_param = self._get_content_parameter_name()
                parameters[content_param] = v
            else:
                # Regular parameter
                parameters[k] = v

        return parameters

    def _get_content_parameter_name(self) -> str:
        """
        Get the appropriate parameter name for content based on the service.

        Returns:
            Parameter name for content
        """
        # Service-specific content parameter mapping
        content_mapping = {
            "gmail": "body",
            "notion": "content",
            "github": "body",
            "slack": "text",
            "discord": "content",
            "twitter": "text",
        }

        return content_mapping.get(self.service_name, "content")

    def _map_action_name(self, action: str) -> str:
        """
        Map user-friendly action names to actual Composio action names.

        This handles the mapping between what users naturally want to call actions
        (like "create_page") and what Composio actually expects ("NOTION_CREATE_NOTION_PAGE").

        Args:
            action: User-friendly action name

        Returns:
            Mapped Composio action name
        """
        # Service-specific action mappings
        action_mappings = {
            "gmail": {
                "send_email": "GMAIL_SEND_EMAIL",
                "send": "GMAIL_SEND_EMAIL",
                "create_draft": "GMAIL_CREATE_EMAIL_DRAFT",
                "draft": "GMAIL_CREATE_EMAIL_DRAFT",
                "fetch_emails": "GMAIL_FETCH_EMAILS",
                "get_emails": "GMAIL_FETCH_EMAILS",
                "read_emails": "GMAIL_FETCH_EMAILS",
                "fetch_message": "GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID",
                "get_message": "GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID",
                "read_message": "GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID",
                "reply": "GMAIL_REPLY_TO_THREAD",
                "reply_to_thread": "GMAIL_REPLY_TO_THREAD",
                "delete_message": "GMAIL_DELETE_MESSAGE",
                "delete": "GMAIL_DELETE_MESSAGE",
                "move_to_trash": "GMAIL_MOVE_TO_TRASH",
                "trash": "GMAIL_MOVE_TO_TRASH",
                "list_drafts": "GMAIL_LIST_DRAFTS",
                "get_drafts": "GMAIL_LIST_DRAFTS",
                "delete_draft": "GMAIL_DELETE_DRAFT",
                "add_label": "GMAIL_ADD_LABEL_TO_EMAIL",
                "create_label": "GMAIL_CREATE_LABEL",
                "list_labels": "GMAIL_LIST_LABELS",
                "get_labels": "GMAIL_LIST_LABELS",
                "remove_label": "GMAIL_REMOVE_LABEL",
                "get_profile": "GMAIL_GET_PROFILE",
                "profile": "GMAIL_GET_PROFILE",
                "get_contacts": "GMAIL_GET_CONTACTS",
                "contacts": "GMAIL_GET_CONTACTS",
                "search_people": "GMAIL_SEARCH_PEOPLE",
                "find_people": "GMAIL_SEARCH_PEOPLE",
            },
            "notion": {
                "create_page": "NOTION_CREATE_NOTION_PAGE",
                "create": "NOTION_CREATE_NOTION_PAGE",
                "new_page": "NOTION_CREATE_NOTION_PAGE",
                "search_page": "NOTION_SEARCH_NOTION_PAGE",
                "search": "NOTION_SEARCH_NOTION_PAGE",
                "search_pages": "NOTION_SEARCH_NOTION_PAGE",
                "find_page": "NOTION_SEARCH_NOTION_PAGE",
                "list_pages": "NOTION_SEARCH_NOTION_PAGE",  # ✅ Fix for the error
                "get_pages": "NOTION_SEARCH_NOTION_PAGE",
                "fetch_row": "NOTION_FETCH_ROW",
                "get_row": "NOTION_FETCH_ROW",
                "get_page": "NOTION_FETCH_ROW",
                "add_content": "NOTION_ADD_PAGE_CONTENT",
                "add_page_content": "NOTION_ADD_PAGE_CONTENT",
                "append_blocks": "NOTION_APPEND_BLOCK_CHILDREN",
                "add_blocks": "NOTION_APPEND_BLOCK_CHILDREN",
                "create_database": "NOTION_CREATE_DATABASE",
                "new_database": "NOTION_CREATE_DATABASE",
                "query_database": "NOTION_QUERY_DATABASE",
                "search_database": "NOTION_QUERY_DATABASE",
                "insert_row": "NOTION_INSERT_ROW_DATABASE",
                "add_row": "NOTION_INSERT_ROW_DATABASE",
                "create_row": "NOTION_INSERT_ROW_DATABASE",
                "update_row": "NOTION_UPDATE_ROW_DATABASE",
                "edit_row": "NOTION_UPDATE_ROW_DATABASE",
                "fetch_database": "NOTION_FETCH_DATABASE",
                "get_database": "NOTION_FETCH_DATABASE",
                "archive_page": "NOTION_ARCHIVE_NOTION_PAGE",
                "archive": "NOTION_ARCHIVE_NOTION_PAGE",
                "delete_block": "NOTION_DELETE_BLOCK",
                "remove_block": "NOTION_DELETE_BLOCK",
                "fetch_block": "NOTION_FETCH_NOTION_BLOCK",
                "get_block": "NOTION_FETCH_NOTION_BLOCK",
                "fetch_children": "NOTION_FETCH_NOTION_CHILD_BLOCK",
                "get_children": "NOTION_FETCH_NOTION_CHILD_BLOCK",
                "get_page_content": "NOTION_FETCH_NOTION_CHILD_BLOCK",  # ✅ Fix for the error
                "fetch_page_content": "NOTION_FETCH_NOTION_CHILD_BLOCK",
                "page_content": "NOTION_FETCH_NOTION_CHILD_BLOCK",
                "create_comment": "NOTION_CREATE_COMMENT",
                "add_comment": "NOTION_CREATE_COMMENT",
                "fetch_comments": "NOTION_FETCH_COMMENTS",
                "get_comments": "NOTION_FETCH_COMMENTS",
                "duplicate_page": "NOTION_DUPLICATE_PAGE",
                "copy_page": "NOTION_DUPLICATE_PAGE",
                "update_block": "NOTION_NOTION_UPDATE_BLOCK",
                "edit_block": "NOTION_NOTION_UPDATE_BLOCK",
                "update_schema": "NOTION_UPDATE_SCHEMA_DATABASE",
                "edit_schema": "NOTION_UPDATE_SCHEMA_DATABASE",
                "about_me": "NOTION_GET_ABOUT_ME",
                "me": "NOTION_GET_ABOUT_ME",
                "about_user": "NOTION_GET_ABOUT_USER",
                "user_info": "NOTION_GET_ABOUT_USER",
                "list_users": "NOTION_LIST_USERS",
                "get_users": "NOTION_LIST_USERS",
                "get_property": "NOTION_GET_PAGE_PROPERTY_ACTION",
                "page_property": "NOTION_GET_PAGE_PROPERTY_ACTION",
            },
            "slack": {
                "send_message": "SLACK_CHAT_POST_MESSAGE",
                "post_message": "SLACK_CHAT_POST_MESSAGE",
                "message": "SLACK_CHAT_POST_MESSAGE",
                "send": "SLACK_CHAT_POST_MESSAGE",
                "add_reaction": "SLACK_ADD_REACTION_TO_AN_ITEM",
                "react": "SLACK_ADD_REACTION_TO_AN_ITEM",
                "create_reminder": "SLACK_CREATE_A_REMINDER",
                "remind": "SLACK_CREATE_A_REMINDER",
                "reminder": "SLACK_CREATE_A_REMINDER",
                "delete_message": "SLACK_DELETES_A_MESSAGE_FROM_A_CHAT",
                "delete": "SLACK_DELETES_A_MESSAGE_FROM_A_CHAT",
                "remove_message": "SLACK_DELETES_A_MESSAGE_FROM_A_CHAT",
                "create_channel": "SLACK_CREATE_CHANNEL_BASED_CONVERSATION",
                "new_channel": "SLACK_CREATE_CHANNEL_BASED_CONVERSATION",
                "archive_channel": "SLACK_ARCHIVE_A_SLACK_CONVERSATION",
                "archive": "SLACK_ARCHIVE_A_SLACK_CONVERSATION",
                "list_channels": "SLACK_CONVERSATIONS_LIST",
                "get_channels": "SLACK_CONVERSATIONS_LIST",
                "channels": "SLACK_CONVERSATIONS_LIST",
                "channel_info": "SLACK_CONVERSATIONS_INFO",
                "get_channel": "SLACK_CONVERSATIONS_INFO",
                "channel_history": "SLACK_CONVERSATIONS_HISTORY",
                "history": "SLACK_CONVERSATIONS_HISTORY",
                "get_history": "SLACK_CONVERSATIONS_HISTORY",
                # Fix for the current error - map list_actions to a valid action
                "list_actions": "SLACK_CONVERSATIONS_LIST",
                "actions": "SLACK_CONVERSATIONS_LIST",
                "list_conversations": "SLACK_CONVERSATIONS_LIST",
                "conversations": "SLACK_CONVERSATIONS_LIST",
            },
            "googledrive": {
                "create_file": "GOOGLEDRIVE_CREATE_FILE_FROM_TEXT",
                "new_file": "GOOGLEDRIVE_CREATE_FILE_FROM_TEXT",
                "create_text_file": "GOOGLEDRIVE_CREATE_FILE_FROM_TEXT",
                "create_folder": "GOOGLEDRIVE_CREATE_FOLDER",
                "new_folder": "GOOGLEDRIVE_CREATE_FOLDER",
                "mkdir": "GOOGLEDRIVE_CREATE_FOLDER",
                "find_file": "GOOGLEDRIVE_FIND_FILE",
                "search_file": "GOOGLEDRIVE_FIND_FILE",
                "search": "GOOGLEDRIVE_FIND_FILE",
                "find_folder": "GOOGLEDRIVE_FIND_FOLDER",
                "search_folder": "GOOGLEDRIVE_FIND_FOLDER",
                "download_file": "GOOGLEDRIVE_DOWNLOAD_FILE",
                "download": "GOOGLEDRIVE_DOWNLOAD_FILE",
                "get_file": "GOOGLEDRIVE_DOWNLOAD_FILE",
                "edit_file": "GOOGLEDRIVE_EDIT_FILE",
                "update_file": "GOOGLEDRIVE_EDIT_FILE",
                "modify_file": "GOOGLEDRIVE_EDIT_FILE",
                "copy_file": "GOOGLEDRIVE_COPY_FILE",
                "copy": "GOOGLEDRIVE_COPY_FILE",
                "duplicate": "GOOGLEDRIVE_COPY_FILE",
                "delete_file": "GOOGLEDRIVE_DELETE_FOLDER_OR_FILE",
                "delete": "GOOGLEDRIVE_DELETE_FOLDER_OR_FILE",
                "remove": "GOOGLEDRIVE_DELETE_FOLDER_OR_FILE",
                "upload_file": "GOOGLEDRIVE_UPLOAD_FILE",
                "upload": "GOOGLEDRIVE_UPLOAD_FILE",
                "share_file": "GOOGLEDRIVE_ADD_FILE_SHARING_PREFERENCE",
                "share": "GOOGLEDRIVE_ADD_FILE_SHARING_PREFERENCE",
                "add_sharing": "GOOGLEDRIVE_ADD_FILE_SHARING_PREFERENCE",
                # Common aliases that might be used
                "list_actions": "GOOGLEDRIVE_FIND_FILE",  # Default to file search
                "actions": "GOOGLEDRIVE_FIND_FILE",
            },
            # Add google_drive alias (same as googledrive)
            "google_drive": {
                "create_file": "GOOGLEDRIVE_CREATE_FILE_FROM_TEXT",
                "new_file": "GOOGLEDRIVE_CREATE_FILE_FROM_TEXT",
                "create_text_file": "GOOGLEDRIVE_CREATE_FILE_FROM_TEXT",
                "create_folder": "GOOGLEDRIVE_CREATE_FOLDER",
                "new_folder": "GOOGLEDRIVE_CREATE_FOLDER",
                "mkdir": "GOOGLEDRIVE_CREATE_FOLDER",
                "find_file": "GOOGLEDRIVE_FIND_FILE",
                "search_file": "GOOGLEDRIVE_FIND_FILE",
                "search": "GOOGLEDRIVE_FIND_FILE",
                "find_folder": "GOOGLEDRIVE_FIND_FOLDER",
                "search_folder": "GOOGLEDRIVE_FIND_FOLDER",
                "download_file": "GOOGLEDRIVE_DOWNLOAD_FILE",
                "download": "GOOGLEDRIVE_DOWNLOAD_FILE",
                "get_file": "GOOGLEDRIVE_DOWNLOAD_FILE",
                "edit_file": "GOOGLEDRIVE_EDIT_FILE",
                "update_file": "GOOGLEDRIVE_EDIT_FILE",
                "modify_file": "GOOGLEDRIVE_EDIT_FILE",
                "copy_file": "GOOGLEDRIVE_COPY_FILE",
                "copy": "GOOGLEDRIVE_COPY_FILE",
                "duplicate": "GOOGLEDRIVE_COPY_FILE",
                "delete_file": "GOOGLEDRIVE_DELETE_FOLDER_OR_FILE",
                "delete": "GOOGLEDRIVE_DELETE_FOLDER_OR_FILE",
                "remove": "GOOGLEDRIVE_DELETE_FOLDER_OR_FILE",
                "upload_file": "GOOGLEDRIVE_UPLOAD_FILE",
                "upload": "GOOGLEDRIVE_UPLOAD_FILE",
                "share_file": "GOOGLEDRIVE_ADD_FILE_SHARING_PREFERENCE",
                "share": "GOOGLEDRIVE_ADD_FILE_SHARING_PREFERENCE",
                "add_sharing": "GOOGLEDRIVE_ADD_FILE_SHARING_PREFERENCE",
                # Common aliases that might be used
                "list_actions": "GOOGLEDRIVE_FIND_FILE",  # Default to file search
                "actions": "GOOGLEDRIVE_FIND_FILE",
            },
        }

        # Get service-specific mappings
        service_mappings = action_mappings.get(self.service_name, {})

        # Try exact match first
        if action in service_mappings:
            return service_mappings[action]

        # Try lowercase match
        action_lower = action.lower()
        if action_lower in service_mappings:
            return service_mappings[action_lower]

        # Try with underscores replaced by spaces and vice versa
        action_underscore = action_lower.replace(" ", "_")
        action_space = action_lower.replace("_", " ")

        if action_underscore in service_mappings:
            return service_mappings[action_underscore]
        if action_space in service_mappings:
            return service_mappings[action_space]

        # Try removing service prefix and mapping (e.g., "NOTION_LIST_PAGES" → "list_pages" → "NOTION_SEARCH_NOTION_PAGE")
        service_prefix = f"{self.service_name.upper()}_"
        if action.upper().startswith(service_prefix):
            action_without_prefix = action[len(service_prefix) :].lower()
            if action_without_prefix in service_mappings:
                return service_mappings[action_without_prefix]

        # If no mapping found, return the original action (might be already correct)
        return action

    def _format_success(self, action: str, data: Any) -> str:
        """
        Format successful execution result.

        Args:
            action: The action that was executed
            data: The result data from Composio

        Returns:
            Formatted success message
        """
        try:
            # Try to format data as JSON if it's a dict/list
            if isinstance(data, (dict, list)):
                data_str = json.dumps(data, indent=2)
            else:
                data_str = str(data)

            return f"✅ {self.service_name.title()} action '{action}' completed successfully.\n\nResult:\n{data_str}"

        except Exception as e:
            logger.warning(f"Error formatting success result: {e}")
            return f"✅ {self.service_name.title()} action '{action}' completed successfully."

    def _format_error(self, error_message: str) -> str:
        """
        Format error message.

        Args:
            error_message: The error message

        Returns:
            Formatted error message
        """
        return f"❌ {self.service_name.title()} tool error: {error_message}"
