# 🔍 Composio XML Integration - Debugging Guide

## Overview

This guide provides comprehensive debugging information for the Composio XML integration, including all potential failure points, logging details, and troubleshooting steps.

## 🚨 Key Failure Points & Logging

### 1. **Environment Setup Failures**

**Potential Issues:**
- Missing environment variables
- Invalid API keys
- Database connection issues

**Logs to Watch:**
```
❌ COMPOSIO_API_KEY environment variable is required
❌ SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables are required
```

**Debug Steps:**
```bash
# Check environment variables
echo $COMPOSIO_API_KEY
echo $SUPABASE_URL
echo $SUPABASE_SERVICE_ROLE_KEY

# Test Composio API key
curl -H "X-API-Key: $COMPOSIO_API_KEY" https://backend.composio.dev/api/v1/apps
```

### 2. **User Connection Discovery Failures**

**Potential Issues:**
- User has no active connections
- Database query failures
- Invalid user_id format

**Logs to Watch:**
```
🔍 _get_user_toolset called for user: {user_id}
⚠️ No connections found in database for user {user_id}
⚠️ No active connections found for user {user_id}
✅ Found {count} active connections for user {user_id}
```

**Debug Steps:**
```sql
-- Check user connections in Supabase
SELECT * FROM user_mcp_connections WHERE user_id = 'your_user_id';
SELECT * FROM user_mcp_connections WHERE user_id = 'your_user_id' AND is_active = true;
```

### 3. **Composio Toolset Creation Failures**

**Potential Issues:**
- Invalid entity_id
- Composio API authentication failures
- Network connectivity issues

**Logs to Watch:**
```
🔧 Creating ComposioToolSet with entity_id={user_id}
❌ Error creating toolset for user {user_id}: {error}
🎉 Successfully created and cached toolset for user {user_id}
```

**Debug Steps:**
```python
# Test Composio toolset creation manually
from composio_openai import ComposioToolSet
toolset = ComposioToolSet(api_key="your_api_key", entity_id="test_user")
```

### 4. **Tool Discovery Failures**

**Potential Issues:**
- Service not supported by Composio
- No actions available for service
- API rate limiting

**Logs to Watch:**
```
📋 Testing get_user_available_tools for user: {user_id}
🔧 Fetching tools for {service_name} (user: {user_id})
✅ Found {count} actions for {service_name}
❌ Error fetching tools for {service_name}: {error}
```

**Debug Steps:**
```python
# Test tool discovery manually
from composio_openai import ComposioToolSet, App
toolset = ComposioToolSet(api_key="your_api_key", entity_id="your_user_id")
tools = toolset.get_tools(apps=[App.GMAIL])
print(f"Found {len(tools)} tools")
```

### 5. **XML Tool Creation Failures**

**Potential Issues:**
- Service not supported in XML factory
- Missing tool class implementation
- Invalid action lists

**Logs to Watch:**
```
🏭 create_user_tools called for user: {user_id}
🎯 Creating XML tool for {service_name} with {count} actions
✅ Created {service_name} XML tool for user {user_id}
⚠️ Could not create XML tool for {service_name} - service not supported
```

**Debug Steps:**
```python
# Check supported services
from agent.tools.composio_xml_factory import ComposioXMLToolFactory
factory = ComposioXMLToolFactory()
print("Supported services:", factory.get_supported_services())
```

### 6. **XML Tool Execution Failures**

**Potential Issues:**
- Invalid action names
- Missing required parameters
- Composio API errors
- Connection expired

**Logs to Watch:**
```
🎯 {service_name} XML tool execute called for user {user_id}
🔍 Extracted action: {action}
❌ Action '{action}' not available for {service_name}
🚀 Executing {service_name} action '{action}' for user {user_id}
🎉 Successfully executed {service_name} action '{action}'
💥 Error executing {action} for user {user_id}: {error}
```

**Debug Steps:**
```python
# Test action execution manually
result = await composio_service.execute_xml_tool(
    user_id="your_user_id",
    service_name="gmail",
    action="GMAIL_SEND_EMAIL",
    parameters={"to": "<EMAIL>", "subject": "Test", "body": "Test"}
)
print(result)
```

### 7. **Agent Integration Failures**

**Potential Issues:**
- Tool registration failures
- ThreadManager issues
- Import errors

**Logs to Watch:**
```
🔗 Loading Composio XML tools for user {user_id}
🏭 Creating ComposioXMLToolFactory
📊 Got {count} XML tools from factory
✅ Added {service_name} XML tool for user {user_id}
🎉 Successfully loaded {count} Composio XML tools
💥 Error loading Composio XML tools for user {user_id}: {error}
```

## 🔧 Debugging Commands

### Enable Debug Logging
```python
import logging
logging.getLogger('services.composio_openai_service').setLevel(logging.DEBUG)
logging.getLogger('agent.tools.composio_xml_factory').setLevel(logging.DEBUG)
logging.getLogger('agent.tools.composio_xml_tool').setLevel(logging.DEBUG)
```

### Test Individual Components
```bash
# Run comprehensive test
python test_composio_xml.py

# Run simple test
python simple_composio_test.py

# Check log files
tail -f composio_xml_test.log
tail -f logs/agentpress_*.log
```

### Manual Testing
```python
# Test service creation
from services.composio_openai_service import ComposioXMLService
service = ComposioXMLService.from_env()

# Test user connections
connections = await service.get_user_active_services("your_user_id")
print(f"Active services: {connections}")

# Test tool creation
from agent.tools.composio_xml_factory import ComposioXMLToolFactory
factory = ComposioXMLToolFactory()
tools = await factory.create_user_tools("your_user_id")
print(f"Created {len(tools)} tools")
```

## 🚨 Common Error Patterns

### 1. **"No entity ID found"**
- **Cause:** User has no active connections in database
- **Solution:** Connect a service via frontend first

### 2. **"Action not found in Composio Action enum"**
- **Cause:** Action name doesn't match Composio's enum
- **Solution:** Check Composio documentation for correct action names

### 3. **"Service not connected or inactive"**
- **Cause:** Connection expired or was revoked
- **Solution:** Reconnect service via frontend

### 4. **"Could not create XML tool for service"**
- **Cause:** Service not implemented in XML factory
- **Solution:** Add service to `service_tool_mapping` in factory

### 5. **"Import error"**
- **Cause:** Missing dependencies or circular imports
- **Solution:** Check Python path and install missing packages

## 📊 Monitoring & Metrics

### Key Metrics to Track
- Tool creation success rate per service
- Action execution success rate
- Connection validation frequency
- Error rates by error type

### Log Analysis Queries
```bash
# Count successful tool creations
grep "✅ Created.*XML tool" logs/agentpress_*.log | wc -l

# Count execution errors
grep "💥 Error executing" logs/agentpress_*.log | wc -l

# Find most common errors
grep "❌" logs/agentpress_*.log | sort | uniq -c | sort -nr
```

## 🔄 Recovery Procedures

### 1. **Clear Tool Cache**
```python
# Clear cached toolsets
service._user_toolsets.clear()
service._tools_cache.clear()
service._schema_cache.clear()
```

### 2. **Refresh User Connections**
```python
# Force refresh user connections
await service.auth_service.refresh_user_connections(user_id)
```

### 3. **Restart Agent with Clean State**
```bash
# Clear logs and restart
rm -f logs/agentpress_*.log
rm -f composio_xml_test.log
# Restart agent process
```

## 🎯 Performance Optimization

### Caching Strategy
- Toolsets cached per user_id
- Tool definitions cached per service combination
- Connection status cached with TTL

### Rate Limiting
- Implement per-user rate limiting for API calls
- Add exponential backoff for failed requests
- Monitor Composio API usage

### Memory Management
- Periodic cache cleanup
- Limit maximum cached toolsets
- Monitor memory usage patterns

## 📞 Support & Escalation

### When to Escalate
1. Composio API consistently failing
2. Database connection issues
3. Memory leaks or performance degradation
4. Security-related errors

### Information to Collect
- Full error logs with stack traces
- User ID and service names involved
- Environment configuration
- Recent changes or deployments
- Performance metrics

This debugging guide should help you quickly identify and resolve issues with the Composio XML integration!
