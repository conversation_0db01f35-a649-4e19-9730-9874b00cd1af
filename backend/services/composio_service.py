"""
Composio Service - Connection Management

This module handles all Composio connection management following official SDK patterns.
"""

# Update imports to include Action
from composio_openai import ComposioToolSet, Action

import os
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone
import asyncio

from composio import ComposioToolSet, Action, App
from composio.exceptions import ConnectedAccountNotFoundError
from services.composio_auth_service import ComposioAuthService

logger = logging.getLogger(__name__)


class ComposioService:
    """
    Composio service for managing connections and executing actions.

    This service follows Composio's official documentation patterns.
    """

    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        """Initialize the Composio service."""
        self.api_key = api_key or os.getenv("COMPOSIO_API_KEY")
        self.base_url = base_url or os.getenv(
            "COMPOSIO_BASE_URL", "https://api.composio.dev"
        )

        if not self.api_key:
            raise ValueError("COMPOSIO_API_KEY environment variable is required")

        # Initialize ComposioAuthService for dynamic connection management
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

        if not supabase_url or not supabase_key:
            raise ValueError("Supabase URL and service role key are required")

        self.auth_service = ComposioAuthService(
            composio_api_key=self.api_key,
            supabase_url=supabase_url,
            supabase_key=supabase_key,
        )
        logger.info("ComposioService initialized with ComposioAuthService")

    async def get_user_connections(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all connections for a user dynamically from Supabase."""
        try:
            # Use ComposioAuthService to get user connections from Supabase
            connections = await self.auth_service.get_user_connections(user_id)

            # Convert UserConnection objects to dictionaries
            connection_dicts = []
            for conn in connections:
                connection_dict = {
                    "id": conn.id,
                    "user_id": conn.user_id,
                    "service_name": conn.service_name,
                    "composio_connection_id": conn.composio_connection_id,
                    "composio_entity_id": conn.composio_entity_id,
                    "mcp_url": conn.mcp_url,
                    "status": conn.status,
                    "auth_config_id": conn.auth_config_id,
                    "created_at": (
                        conn.created_at.isoformat() if conn.created_at else None
                    ),
                    "updated_at": (
                        conn.updated_at.isoformat() if conn.updated_at else None
                    ),
                    "last_verified_at": (
                        conn.last_verified_at.isoformat()
                        if conn.last_verified_at
                        else None
                    ),
                    "is_active": conn.is_active,
                    "connected_account_id": conn.connected_account_id,
                    "expires_at": (
                        conn.expires_at.isoformat() if conn.expires_at else None
                    ),
                    "refresh_token": conn.refresh_token,
                    "error_message": conn.error_message,
                }
                connection_dicts.append(connection_dict)

            logger.info(
                f"Retrieved {len(connection_dicts)} connections for user {user_id}"
            )
            return connection_dicts

        except Exception as e:
            logger.error(f"Failed to get user connections for {user_id}: {e}")
            # Return empty list instead of raising to maintain compatibility
            return []

    async def get_user_active_services(self, user_id: str) -> List[str]:
        """Get active services for a user."""
        connections = await self.get_user_connections(user_id)
        return [
            conn["service_name"] for conn in connections if conn["status"] == "active"
        ]

    def get_user_active_services_sync(self, user_id: str) -> List[str]:
        """Synchronous version that works in both sync and async contexts."""
        try:
            # Use asyncio to run the async method
            import asyncio

            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an async context, we can't use run()
                # This is a limitation - in sync contexts within async, we need a different approach
                logger.warning(
                    "Cannot run async method from within async context - returning empty list"
                )
                return []
            else:
                return loop.run_until_complete(self.get_user_active_services(user_id))
        except Exception as e:
            logger.error(f"Failed to get active services sync for {user_id}: {e}")
            return []

    async def update_connection_verification(
        self, user_id: str, connection_id: str
    ) -> bool:
        """Update the last_verified_at timestamp for a connection."""
        try:
            return await self.auth_service.update_connection_verification(
                user_id, connection_id
            )
        except Exception as e:
            logger.error(f"Failed to update connection verification: {e}")
            return False

    async def update_connection_status(
        self, user_id: str, connection_id: str, status: str
    ) -> bool:
        """Update connection status."""
        try:
            # Note: ComposioAuthService doesn't have this exact method,
            # but we can use the Supabase client directly
            # For now, we'll implement a basic version
            logger.info(f"Updating connection {connection_id} status to {status}")
            # This would need to be implemented in ComposioAuthService if needed
            return True
        except Exception as e:
            logger.error(f"Failed to update connection status: {e}")
            return False

    async def verify_connection(
        self, user_id: str, service_name: str
    ) -> Dict[str, Any]:
        """
        Verify that a connection is active and working using dynamic data from Supabase.
        """
        try:
            # Get the connection from Supabase
            connection = await self.auth_service.get_connection_by_service(
                user_id, service_name
            )

            if not connection:
                return {
                    "connected": False,
                    "status": "not_found",
                    "error": f"No {service_name} connection found for user",
                    "verified": False,
                }

            # Try to verify with Composio API using the real connection data
            try:
                # Initialize Composio toolset with user's entity ID
                toolset = ComposioToolSet(entity_id=connection.composio_entity_id)

                # Try to get the entity and connection
                entity = toolset.get_entity(id=connection.composio_entity_id)
                app_enum = getattr(App, service_name.upper())
                connection_account = entity.get_connection(app=app_enum)

                # Connection is valid in Composio
                logger.info(
                    f"✅ Successfully verified {service_name} connection with Composio API"
                )

                # Update verification timestamp
                await self.update_connection_verification(
                    user_id, connection.composio_connection_id
                )

                return {
                    "connected": True,
                    "status": "active",
                    "connection_id": connection.composio_connection_id,
                    "verified": True,
                    "last_verified_at": datetime.now(timezone.utc).isoformat(),
                }

            except Exception as e:
                logger.warning(f"❌ Failed to verify with Composio API: {e}")

                # Return the stored status from Supabase
                return {
                    "connected": connection.status == "active",
                    "status": connection.status,
                    "connection_id": connection.composio_connection_id,
                    "verified": False,
                    "error": f"Composio API verification failed: {str(e)}",
                    "last_verified_at": (
                        connection.last_verified_at.isoformat()
                        if connection.last_verified_at
                        else None
                    ),
                }

        except Exception as e:
            logger.error(f"Error in verify_connection: {e}")
            return {
                "connected": False,
                "status": "error",
                "error": str(e),
                "verified": False,
            }

    async def execute_tool_action(
        self,
        user_id: str,
        service_name: str,
        action: Union[str, Action],
        params: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Execute a tool action for a user using dynamic connection data.

        Args:
            user_id: The user's ID
            service_name: The service name (gmail, notion, etc.)
            action: The action to execute (string or Action enum)
            params: Parameters for the action

        Returns:
            The action result
        """
        try:
            # Get the user's connection for this service from Supabase
            connection = await self.auth_service.get_connection_by_service(
                user_id, service_name
            )

            if not connection:
                raise ValueError(
                    f"No active {service_name} connection found for user {user_id}"
                )

            if connection.status != "active":
                raise ValueError(
                    f"{service_name} connection is not active (status: {connection.status})"
                )

            # Use the real entity_id from the connection
            entity_id = connection.composio_entity_id

            # Map our action strings to Composio Action enums
            action_map = {
                # Gmail actions
                "GMAIL_SEND": Action.GMAIL_SEND_EMAIL,
                "GMAIL_READ": Action.GMAIL_READ_EMAIL,
                "GMAIL_SEARCH": Action.GMAIL_SEARCH_EMAILS,
                "GMAIL_STATUS": Action.GMAIL_GET_STATUS,
                # Notion actions
                "NOTION_CREATE_PAGE": Action.NOTION_CREATE_PAGE,
                "NOTION_READ_PAGE": Action.NOTION_READ_PAGE,
                "NOTION_SEARCH": Action.NOTION_SEARCH,
                "NOTION_STATUS": Action.NOTION_GET_STATUS,
            }

            # Convert string action to enum if needed
            action_enum = action
            if isinstance(action, str):
                if action in action_map:
                    action_enum = action_map[action]
                    logger.info(f"Mapped string action {action} to enum {action_enum}")
                else:
                    # Try to get the action enum directly
                    try:
                        action_enum = getattr(Action, action)
                    except AttributeError:
                        # If still not found, raise error
                        raise ValueError(f"Unknown action: {action}")

            logger.info(
                f"Executing {service_name} action {action_enum} with params: {params}"
            )

            # Initialize toolset with the user's entity_id from Supabase
            toolset = ComposioToolSet(entity_id=entity_id)

            # Execute the actual API call
            logger.info(f"Making Composio API call for action: {action_enum}")
            result = toolset.execute_action(
                action=action_enum, params=params, entity_id=entity_id
            )
            logger.info(f"Composio API call successful")

            return result

        except Exception as e:
            logger.error(f"Failed to execute {service_name} action {action}: {e}")
            raise

    async def get_connection_by_service(self, user_id: str, service_name: str):
        """Get connection for a specific service - delegate to auth service."""
        return await self.auth_service.get_connection_by_service(user_id, service_name)

    async def check_connection_status(self, user_id: str, service_name: str):
        """Check connection status - delegate to auth service."""
        return await self.auth_service.check_connection_status(user_id, service_name)

    async def disconnect_service(self, user_id: str, connection_id: str) -> bool:
        """Disconnect a service - delegate to auth service."""
        return await self.auth_service.disconnect_service(user_id, connection_id)

    async def get_available_integrations(self):
        """Get available integrations - delegate to auth service."""
        return await self.auth_service.get_available_integrations()

    async def cleanup_expired_oauth_states(self):
        """Clean up expired OAuth states - delegate to auth service."""
        return await self.auth_service.cleanup_expired_oauth_states()

    @classmethod
    def from_env(cls):
        """Create an instance from environment variables."""
        return cls(
            api_key=os.getenv("COMPOSIO_API_KEY"),
            base_url=os.getenv("COMPOSIO_BASE_URL"),
        )
