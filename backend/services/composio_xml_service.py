"""
Enhanced Composio XML Service with Parameter Mapping and Response Processing

This service integrates Composio's processor system for bulletproof parameter mapping
and response processing, following official Composio patterns.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Union
from composio import ComposioToolSet, Action
from composio.client.exceptions import ComposioClientError

from .composio_processors import (
    get_all_processors,
    get_gmail_processors, 
    get_notion_processors,
    create_parameter_mapper,
    create_response_processor
)

logger = logging.getLogger(__name__)


class EnhancedComposioXMLService:
    """
    Enhanced Composio XML service with built-in parameter mapping and response processing.
    
    This service uses Composio's official processor system to handle:
    - Parameter mapping (e.g., "to" -> "recipient_email")
    - Response processing and cleaning
    - Schema enhancement with aliases
    """
    
    def __init__(self, api_key: str, entity_id: str):
        """Initialize the enhanced Composio XML service."""
        self.api_key = api_key
        self.entity_id = entity_id
        self.toolset = ComposioToolSet(entity_id=entity_id)
        
        # Get all processors for supported services
        self.processors = get_all_processors()
        
        logger.info(f"Enhanced Composio XML service initialized for entity {entity_id}")
        logger.info(f"Loaded processors for {len(self.processors['pre'])} actions")
    
    async def get_user_tools_with_processors(self, user_id: str, services: List[str]) -> List[Dict[str, Any]]:
        """
        Get tools for user with processors applied.
        
        Args:
            user_id: User ID (used as entity_id)
            services: List of service names (e.g., ['gmail', 'notion'])
            
        Returns:
            List of tool definitions with processors applied
        """
        try:
            # Map service names to actions
            actions_to_fetch = []
            
            for service in services:
                if service.lower() == "gmail":
                    actions_to_fetch.extend([
                        Action.GMAIL_SEND_EMAIL,
                        Action.GMAIL_READ_EMAIL, 
                        Action.GMAIL_SEARCH_EMAILS
                    ])
                elif service.lower() == "notion":
                    actions_to_fetch.extend([
                        Action.NOTION_CREATE_PAGE,
                        Action.NOTION_READ_PAGE,
                        Action.NOTION_SEARCH
                    ])
            
            if not actions_to_fetch:
                logger.warning(f"No supported actions found for services: {services}")
                return []
            
            # Get tools with processors applied
            tools = self.toolset.get_tools(
                actions=actions_to_fetch,
                processors=self.processors
            )
            
            logger.info(f"Retrieved {len(tools)} tools with processors for user {user_id}")
            return tools
            
        except Exception as e:
            logger.error(f"Failed to get tools with processors for user {user_id}: {e}")
            return []
    
    async def execute_tool_with_processing(
        self, 
        user_id: str, 
        service_name: str, 
        action: str, 
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute a tool action with full parameter mapping and response processing.
        
        Args:
            user_id: User ID
            service_name: Service name (gmail, notion, etc.)
            action: Action name (GMAIL_SEND_EMAIL, etc.)
            parameters: Raw parameters (may use common names like "to", "subject")
            
        Returns:
            Processed response with clean structure
        """
        try:
            # Step 1: Map parameters using our processor
            param_mapper = create_parameter_mapper(service_name, action)
            mapped_params = param_mapper(parameters)
            
            logger.info(f"Executing {service_name}.{action} with mapped parameters")
            logger.debug(f"Original params: {parameters}")
            logger.debug(f"Mapped params: {mapped_params}")
            
            # Step 2: Get the Action enum
            action_enum = self._get_action_enum(action)
            if not action_enum:
                raise ValueError(f"Unknown action: {action}")
            
            # Step 3: Execute the action
            raw_response = self.toolset.execute_action(
                action=action_enum,
                params=mapped_params,
                entity_id=self.entity_id
            )
            
            logger.info(f"Raw Composio response received for {service_name}.{action}")
            logger.debug(f"Raw response: {raw_response}")
            
            # Step 4: Process the response
            response_processor = create_response_processor(service_name, action)
            processed_response = response_processor(raw_response)
            
            logger.info(f"Successfully executed and processed {service_name}.{action}")
            return processed_response
            
        except Exception as e:
            logger.error(f"Failed to execute {service_name}.{action}: {e}")
            
            # Return a structured error response
            return {
                "success": False,
                "error": str(e),
                "service": service_name,
                "action": action,
                "parameters": parameters
            }
    
    def _get_action_enum(self, action: str) -> Optional[Action]:
        """Get Action enum from string action name."""
        try:
            # Direct lookup
            if hasattr(Action, action):
                return getattr(Action, action)
            
            # Try uppercase
            if hasattr(Action, action.upper()):
                return getattr(Action, action.upper())
            
            # Common action mappings
            action_mappings = {
                "GMAIL_SEND": Action.GMAIL_SEND_EMAIL,
                "GMAIL_READ": Action.GMAIL_READ_EMAIL,
                "GMAIL_SEARCH": Action.GMAIL_SEARCH_EMAILS,
                "NOTION_CREATE": Action.NOTION_CREATE_PAGE,
                "NOTION_READ": Action.NOTION_READ_PAGE,
                "NOTION_SEARCH": Action.NOTION_SEARCH
            }
            
            if action in action_mappings:
                return action_mappings[action]
            
            logger.warning(f"Could not find Action enum for: {action}")
            return None
            
        except Exception as e:
            logger.error(f"Error getting Action enum for {action}: {e}")
            return None
    
    async def validate_parameters(
        self, 
        service_name: str, 
        action: str, 
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate parameters against the actual Composio schema.
        
        Returns:
            Dictionary with validation results
        """
        try:
            action_enum = self._get_action_enum(action)
            if not action_enum:
                return {
                    "valid": False,
                    "error": f"Unknown action: {action}"
                }
            
            # Get the schema for this action
            schemas = self.toolset.get_action_schemas(
                actions=[action_enum],
                check_connected_accounts=False
            )
            
            if not schemas:
                return {
                    "valid": False,
                    "error": f"No schema found for action: {action}"
                }
            
            schema = schemas[0]
            required_fields = schema.parameters.get("required", [])
            available_fields = schema.parameters.get("properties", {}).keys()
            
            # Map parameters first
            param_mapper = create_parameter_mapper(service_name, action)
            mapped_params = param_mapper(parameters)
            
            # Check required fields
            missing_required = [
                field for field in required_fields 
                if field not in mapped_params
            ]
            
            # Check invalid fields
            invalid_fields = [
                field for field in mapped_params.keys() 
                if field not in available_fields
            ]
            
            validation_result = {
                "valid": len(missing_required) == 0 and len(invalid_fields) == 0,
                "missing_required": missing_required,
                "invalid_fields": invalid_fields,
                "mapped_parameters": mapped_params,
                "schema": schema.parameters
            }
            
            if not validation_result["valid"]:
                error_parts = []
                if missing_required:
                    error_parts.append(f"Missing required fields: {missing_required}")
                if invalid_fields:
                    error_parts.append(f"Invalid fields: {invalid_fields}")
                validation_result["error"] = "; ".join(error_parts)
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Parameter validation error for {service_name}.{action}: {e}")
            return {
                "valid": False,
                "error": f"Validation failed: {str(e)}"
            }
    
    async def get_service_actions(self, service_name: str) -> List[str]:
        """Get available actions for a service."""
        service_actions = {
            "gmail": ["GMAIL_SEND_EMAIL", "GMAIL_READ_EMAIL", "GMAIL_SEARCH_EMAILS"],
            "notion": ["NOTION_CREATE_PAGE", "NOTION_READ_PAGE", "NOTION_SEARCH"]
        }
        
        return service_actions.get(service_name.lower(), [])
    
    async def test_connection(self, service_name: str) -> Dict[str, Any]:
        """Test connection to a service."""
        try:
            # Try to get a simple action for the service
            if service_name.lower() == "gmail":
                test_action = Action.GMAIL_GET_STATUS
            elif service_name.lower() == "notion":
                test_action = Action.NOTION_GET_STATUS
            else:
                return {
                    "connected": False,
                    "error": f"Unsupported service: {service_name}"
                }
            
            # Try to execute a status check
            result = self.toolset.execute_action(
                action=test_action,
                params={},
                entity_id=self.entity_id
            )
            
            return {
                "connected": True,
                "service": service_name,
                "status": "active",
                "test_result": result
            }
            
        except Exception as e:
            logger.error(f"Connection test failed for {service_name}: {e}")
            return {
                "connected": False,
                "service": service_name,
                "error": str(e)
            }
