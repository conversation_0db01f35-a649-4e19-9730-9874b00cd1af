"""
Composio Tool Processors - Parameter Mapping and Response Processing

This module implements Composio's official processor system for handling
parameter mapping and response processing at scale.

Based on Composio's documentation: https://docs.composio.dev/tool-calling/processing-tools
"""

import logging
from typing import Dict, Any, Callable, Optional, List
from composio import Action

logger = logging.getLogger(__name__)

# ============================================================================
# PARAMETER MAPPING CONFIGURATIONS
# ============================================================================

# Central mapping configuration for all services
# Based on actual Composio tool definitions from composio_tool_defs/
PARAMETER_MAPPINGS = {
    "gmail": {
        # Gmail Send Email - GMAIL_SEND_EMAIL
        "GMAIL_SEND_EMAIL": {
            "to": "recipient_email",
            "from": "sender_email",
            "cc": "cc",
            "bcc": "bcc",
            "subject": "subject",
            "body": "body",
            "message": "body",
            "content": "body",
            "text": "body",
            "html": "body",
            "attachment": "attachment",
            "attachments": "attachment",
            "is_html": "is_html",
            "extra_recipients": "extra_recipients",
            "user_id": "user_id",
        },
        # Gmail Create Draft - GMAIL_CREATE_EMAIL_DRAFT
        "GMAIL_CREATE_EMAIL_DRAFT": {
            "to": "recipient_email",
            "cc": "cc",
            "bcc": "bcc",
            "subject": "subject",
            "body": "body",
            "message": "body",
            "content": "body",
            "text": "body",
            "attachment": "attachment",
            "is_html": "is_html",
            "extra_recipients": "extra_recipients",
            "thread_id": "thread_id",
            "user_id": "user_id",
        },
        # Gmail Fetch Emails - GMAIL_FETCH_EMAILS
        "GMAIL_FETCH_EMAILS": {
            "query": "query",
            "q": "query",
            "search": "query",
            "limit": "max_results",
            "max_results": "max_results",
            "count": "max_results",
            "include_payload": "include_payload",
            "include_spam_trash": "include_spam_trash",
            "label_ids": "label_ids",
            "page_token": "page_token",
            "user_id": "user_id",
        },
        # Gmail Fetch Message by ID - GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID
        "GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID": {
            "id": "message_id",
            "message_id": "message_id",
            "email_id": "message_id",
            "format": "format",
            "user_id": "user_id",
        },
        # Gmail Reply to Thread - GMAIL_REPLY_TO_THREAD
        "GMAIL_REPLY_TO_THREAD": {
            "to": "recipient_email",
            "cc": "cc",
            "bcc": "bcc",
            "body": "message_body",
            "message": "message_body",
            "content": "message_body",
            "text": "message_body",
            "thread_id": "thread_id",
            "is_html": "is_html",
            "extra_recipients": "extra_recipients",
            "user_id": "user_id",
        },
        # Gmail Delete Message - GMAIL_DELETE_MESSAGE
        "GMAIL_DELETE_MESSAGE": {
            "id": "message_id",
            "message_id": "message_id",
            "email_id": "message_id",
            "user_id": "user_id",
        },
        # Gmail Move to Trash - GMAIL_MOVE_TO_TRASH
        "GMAIL_MOVE_TO_TRASH": {
            "id": "message_id",
            "message_id": "message_id",
            "email_id": "message_id",
            "user_id": "user_id",
        },
        # Gmail List Drafts - GMAIL_LIST_DRAFTS
        "GMAIL_LIST_DRAFTS": {
            "limit": "max_results",
            "max_results": "max_results",
            "count": "max_results",
            "page_token": "page_token",
            "user_id": "user_id",
        },
        # Gmail Delete Draft - GMAIL_DELETE_DRAFT
        "GMAIL_DELETE_DRAFT": {
            "id": "draft_id",
            "draft_id": "draft_id",
            "user_id": "user_id",
        },
        # Gmail Add Label - GMAIL_ADD_LABEL_TO_EMAIL
        "GMAIL_ADD_LABEL_TO_EMAIL": {
            "id": "message_id",
            "message_id": "message_id",
            "email_id": "message_id",
            "add_label_ids": "add_label_ids",
            "remove_label_ids": "remove_label_ids",
            "user_id": "user_id",
        },
        # Gmail Create Label - GMAIL_CREATE_LABEL
        "GMAIL_CREATE_LABEL": {
            "name": "label_name",
            "label_name": "label_name",
            "label_list_visibility": "label_list_visibility",
            "message_list_visibility": "message_list_visibility",
            "user_id": "user_id",
        },
        # Gmail List Labels - GMAIL_LIST_LABELS
        "GMAIL_LIST_LABELS": {"user_id": "user_id"},
        # Gmail Remove Label - GMAIL_REMOVE_LABEL
        "GMAIL_REMOVE_LABEL": {
            "id": "label_id",
            "label_id": "label_id",
            "user_id": "user_id",
        },
        # Gmail Get Profile - GMAIL_GET_PROFILE
        "GMAIL_GET_PROFILE": {"user_id": "user_id"},
        # Gmail Get Contacts - GMAIL_GET_CONTACTS
        "GMAIL_GET_CONTACTS": {
            "page_token": "page_token",
            "person_fields": "person_fields",
            "resource_name": "resource_name",
        },
        # Gmail Search People - GMAIL_SEARCH_PEOPLE
        "GMAIL_SEARCH_PEOPLE": {
            "query": "query",
            "q": "query",
            "search": "query",
            "other_contacts": "other_contacts",
            "page_size": "pageSize",
            "person_fields": "person_fields",
        },
    },
    "notion": {
        # Notion Create Page - NOTION_CREATE_NOTION_PAGE
        "NOTION_CREATE_NOTION_PAGE": {
            "title": "title",
            "name": "title",
            "parent": "parent_id",
            "parent_id": "parent_id",
            "cover": "cover",
            "icon": "icon",
            # Note: content is handled separately via NOTION_ADD_PAGE_CONTENT
            # These mappings help users understand the workflow
            "content": "_content_for_later",
            "body": "_content_for_later",
            "text": "_content_for_later",
        },
        # Notion Search Page - NOTION_SEARCH_NOTION_PAGE
        "NOTION_SEARCH_NOTION_PAGE": {
            "query": "query",
            "q": "query",
            "search": "query",
            "limit": "page_size",
            "max_results": "page_size",
            "count": "page_size",
            "direction": "direction",
            "filter_property": "filter_property",
            "filter_value": "filter_value",
            "start_cursor": "start_cursor",
            "timestamp": "timestamp",
        },
        # Notion Fetch Row (Page) - NOTION_FETCH_ROW
        "NOTION_FETCH_ROW": {
            "id": "page_id",
            "page_id": "page_id",
            "row_id": "page_id",
        },
        # Notion Add Page Content - NOTION_ADD_PAGE_CONTENT
        "NOTION_ADD_PAGE_CONTENT": {
            "parent_id": "parent_block_id",
            "parent_block_id": "parent_block_id",
            "content": "content_block",
            "content_block": "content_block",
            "after": "after",
        },
        # Notion Append Block Children - NOTION_APPEND_BLOCK_CHILDREN
        "NOTION_APPEND_BLOCK_CHILDREN": {
            "id": "block_id",
            "block_id": "block_id",
            "children": "children",
            "after": "after",
        },
        # Notion Create Database - NOTION_CREATE_DATABASE
        "NOTION_CREATE_DATABASE": {
            "parent": "parent_id",
            "parent_id": "parent_id",
            "title": "title",
            "name": "title",
            "properties": "properties",
        },
        # Notion Query Database - NOTION_QUERY_DATABASE
        "NOTION_QUERY_DATABASE": {
            "id": "database_id",
            "database_id": "database_id",
            "limit": "page_size",
            "max_results": "page_size",
            "count": "page_size",
            "sorts": "sorts",
            "start_cursor": "start_cursor",
        },
        # Notion Insert Row Database - NOTION_INSERT_ROW_DATABASE
        "NOTION_INSERT_ROW_DATABASE": {
            "id": "database_id",
            "database_id": "database_id",
            "properties": "properties",
            "child_blocks": "child_blocks",
            "cover": "cover",
            "icon": "icon",
        },
        # Notion Update Row Database - NOTION_UPDATE_ROW_DATABASE
        "NOTION_UPDATE_ROW_DATABASE": {
            "id": "row_id",
            "row_id": "row_id",
            "page_id": "row_id",
            "properties": "properties",
            "cover": "cover",
            "icon": "icon",
            "delete_row": "delete_row",
        },
        # Notion Fetch Database - NOTION_FETCH_DATABASE
        "NOTION_FETCH_DATABASE": {"id": "database_id", "database_id": "database_id"},
        # Notion Archive Page - NOTION_ARCHIVE_NOTION_PAGE
        "NOTION_ARCHIVE_NOTION_PAGE": {
            "id": "page_id",
            "page_id": "page_id",
            "archive": "archive",
        },
        # Notion Delete Block - NOTION_DELETE_BLOCK
        "NOTION_DELETE_BLOCK": {"id": "block_id", "block_id": "block_id"},
        # Notion Fetch Block - NOTION_FETCH_NOTION_BLOCK
        "NOTION_FETCH_NOTION_BLOCK": {"id": "block_id", "block_id": "block_id"},
        # Notion Fetch Child Blocks - NOTION_FETCH_NOTION_CHILD_BLOCK
        "NOTION_FETCH_NOTION_CHILD_BLOCK": {
            "id": "block_id",
            "block_id": "block_id",
            "limit": "page_size",
            "max_results": "page_size",
            "count": "page_size",
            "start_cursor": "start_cursor",
        },
        # Notion Create Comment - NOTION_CREATE_COMMENT
        "NOTION_CREATE_COMMENT": {
            "comment": "comment",
            "discussion_id": "discussion_id",
            "parent_page_id": "parent_page_id",
        },
        # Notion Fetch Comments - NOTION_FETCH_COMMENTS
        "NOTION_FETCH_COMMENTS": {
            "id": "block_id",
            "block_id": "block_id",
            "limit": "page_size",
            "max_results": "page_size",
            "count": "page_size",
            "start_cursor": "start_cursor",
        },
        # Notion Duplicate Page - NOTION_DUPLICATE_PAGE
        "NOTION_DUPLICATE_PAGE": {
            "id": "page_id",
            "page_id": "page_id",
            "parent": "parent_id",
            "parent_id": "parent_id",
            "title": "title",
            "name": "title",
        },
        # Notion Update Block - NOTION_NOTION_UPDATE_BLOCK
        "NOTION_NOTION_UPDATE_BLOCK": {
            "id": "block_id",
            "block_id": "block_id",
            "type": "block_type",
            "block_type": "block_type",
            "content": "content",
            "additional_properties": "additional_properties",
        },
        # Notion Update Schema Database - NOTION_UPDATE_SCHEMA_DATABASE
        "NOTION_UPDATE_SCHEMA_DATABASE": {
            "id": "database_id",
            "database_id": "database_id",
            "title": "title",
            "name": "title",
            "description": "description",
            "properties": "properties",
        },
        # Notion Get About Me - NOTION_GET_ABOUT_ME
        "NOTION_GET_ABOUT_ME": {},
        # Notion Get About User - NOTION_GET_ABOUT_USER
        "NOTION_GET_ABOUT_USER": {"id": "user_id", "user_id": "user_id"},
        # Notion List Users - NOTION_LIST_USERS
        "NOTION_LIST_USERS": {
            "limit": "page_size",
            "max_results": "page_size",
            "count": "page_size",
            "start_cursor": "start_cursor",
        },
        # Notion Get Page Property - NOTION_GET_PAGE_PROPERTY_ACTION
        "NOTION_GET_PAGE_PROPERTY_ACTION": {
            "id": "page_id",
            "page_id": "page_id",
            "property_id": "property_id",
            "limit": "page_size",
            "max_results": "page_size",
            "count": "page_size",
            "start_cursor": "start_cursor",
        },
    },
    "slack": {
        # Slack Send Message - SLACK_CHAT_POST_MESSAGE
        "SLACK_CHAT_POST_MESSAGE": {
            "channel": "channel",
            "text": "text",
            "message": "text",
            "content": "text",
            "body": "text",
            "as_user": "as_user",
            "attachments": "attachments",
            "blocks": "blocks",
            "icon_emoji": "icon_emoji",
            "icon_url": "icon_url",
            "link_names": "link_names",
            "mrkdwn": "mrkdwn",
            "parse": "parse",
            "reply_broadcast": "reply_broadcast",
            "thread_ts": "thread_ts",
            "unfurl_links": "unfurl_links",
            "unfurl_media": "unfurl_media",
            "username": "username",
        },
        # Slack Add Reaction - SLACK_ADD_REACTION_TO_AN_ITEM
        "SLACK_ADD_REACTION_TO_AN_ITEM": {
            "channel": "channel",
            "name": "name",
            "emoji": "name",
            "reaction": "name",
            "timestamp": "timestamp",
            "ts": "timestamp",
        },
        # Slack Create Reminder - SLACK_CREATE_A_REMINDER
        "SLACK_CREATE_A_REMINDER": {
            "text": "text",
            "message": "text",
            "content": "text",
            "reminder": "text",
            "time": "time",
            "when": "time",
            "user": "user",
            "user_id": "user",
        },
        # Slack Delete Message - SLACK_DELETES_A_MESSAGE_FROM_A_CHAT
        "SLACK_DELETES_A_MESSAGE_FROM_A_CHAT": {
            "channel": "channel",
            "ts": "ts",
            "timestamp": "ts",
            "message_ts": "ts",
        },
        # Slack Create Channel - SLACK_CREATE_CHANNEL_BASED_CONVERSATION
        "SLACK_CREATE_CHANNEL_BASED_CONVERSATION": {
            "name": "name",
            "channel_name": "name",
            "is_private": "is_private",
            "private": "is_private",
        },
        # Slack Archive Conversation - SLACK_ARCHIVE_A_SLACK_CONVERSATION
        "SLACK_ARCHIVE_A_SLACK_CONVERSATION": {
            "channel": "channel",
            "channel_id": "channel",
        },
        # Slack List Conversations - SLACK_CONVERSATIONS_LIST
        "SLACK_CONVERSATIONS_LIST": {
            "cursor": "cursor",
            "exclude_archived": "exclude_archived",
            "limit": "limit",
            "types": "types",
        },
        # Slack Conversation Info - SLACK_CONVERSATIONS_INFO
        "SLACK_CONVERSATIONS_INFO": {
            "channel": "channel",
            "channel_id": "channel",
            "include_locale": "include_locale",
            "include_num_members": "include_num_members",
        },
        # Slack Conversation History - SLACK_CONVERSATIONS_HISTORY
        "SLACK_CONVERSATIONS_HISTORY": {
            "channel": "channel",
            "channel_id": "channel",
            "cursor": "cursor",
            "inclusive": "inclusive",
            "latest": "latest",
            "limit": "limit",
            "oldest": "oldest",
        },
        # Slack Create Channel - SLACK_CREATE_CHANNEL_BASED_CONVERSATION
        "SLACK_CREATE_CHANNEL_BASED_CONVERSATION": {
            "name": "name",
            "channel_name": "name",
            "description": "description",
            "is_private": "is_private",
            "private": "is_private",
            "org_wide": "org_wide",
            "team_id": "team_id",
        },
        # Slack Archive Channel - SLACK_ARCHIVE_A_SLACK_CONVERSATION
        "SLACK_ARCHIVE_A_SLACK_CONVERSATION": {
            "channel": "channel",
            "channel_id": "channel",
        },
        # Slack List Channels - SLACK_CONVERSATIONS_LIST
        "SLACK_CONVERSATIONS_LIST": {
            "cursor": "cursor",
            "exclude_archived": "exclude_archived",
            "limit": "limit",
            "max_results": "limit",
            "count": "limit",
            "types": "types",
        },
        # Slack Get Channel Info - SLACK_CONVERSATIONS_INFO
        "SLACK_CONVERSATIONS_INFO": {
            "channel": "channel",
            "channel_id": "channel",
            "include_locale": "include_locale",
            "include_num_members": "include_num_members",
        },
        # Slack Get Channel History - SLACK_CONVERSATIONS_HISTORY
        "SLACK_CONVERSATIONS_HISTORY": {
            "channel": "channel",
            "channel_id": "channel",
            "cursor": "cursor",
            "inclusive": "inclusive",
            "latest": "latest",
            "limit": "limit",
            "max_results": "limit",
            "count": "limit",
            "oldest": "oldest",
        },
    },
    "googledrive": {
        # Google Drive Create File - GOOGLEDRIVE_CREATE_FILE_FROM_TEXT
        "GOOGLEDRIVE_CREATE_FILE_FROM_TEXT": {
            "name": "file_name",
            "file_name": "file_name",
            "filename": "file_name",
            "content": "text_content",
            "text_content": "text_content",
            "text": "text_content",
            "body": "text_content",
            "mime_type": "mime_type",
            "parent": "parent_id",
            "parent_id": "parent_id",
            "folder": "parent_id",
        },
        # Google Drive Create Folder - GOOGLEDRIVE_CREATE_FOLDER
        "GOOGLEDRIVE_CREATE_FOLDER": {
            "name": "folder_name",
            "folder_name": "folder_name",
            "title": "folder_name",
            "parent": "parent_id",
            "parent_id": "parent_id",
            "folder": "parent_id",
        },
        # Google Drive Find File - GOOGLEDRIVE_FIND_FILE
        "GOOGLEDRIVE_FIND_FILE": {
            "name": "name_exact",
            "filename": "name_exact",
            "name_contains": "name_contains",
            "name_not_contains": "name_not_contains",
            "query": "full_text_contains",
            "search": "full_text_contains",
            "full_text_contains": "full_text_contains",
            "full_text_not_contains": "full_text_not_contains",
            "folder": "folder_id",
            "folder_id": "folder_id",
            "parent": "folder_id",
            "mime_type": "mime_type",
            "modified_after": "modified_after",
            "limit": "page_size",
            "max_results": "page_size",
            "count": "page_size",
            "page_size": "page_size",
            "page_token": "page_token",
            "starred": "starred",
            "include_items_from_all_drives": "include_items_from_all_drives",
            "supports_all_drives": "supports_all_drives",
        },
        # Google Drive Find Folder - GOOGLEDRIVE_FIND_FOLDER
        "GOOGLEDRIVE_FIND_FOLDER": {
            "name": "name_exact",
            "folder_name": "name_exact",
            "name_contains": "name_contains",
            "name_not_contains": "name_not_contains",
            "query": "full_text_contains",
            "search": "full_text_contains",
            "full_text_contains": "full_text_contains",
            "full_text_not_contains": "full_text_not_contains",
            "modified_after": "modified_after",
            "starred": "starred",
        },
        # Google Drive Download File - GOOGLEDRIVE_DOWNLOAD_FILE
        "GOOGLEDRIVE_DOWNLOAD_FILE": {
            "id": "file_id",
            "file_id": "file_id",
            "mime_type": "mime_type",
        },
        # Google Drive Edit File - GOOGLEDRIVE_EDIT_FILE
        "GOOGLEDRIVE_EDIT_FILE": {
            "id": "file_id",
            "file_id": "file_id",
            "content": "content",
            "text": "content",
            "body": "content",
            "mime_type": "mime_type",
        },
        # Google Drive Copy File - GOOGLEDRIVE_COPY_FILE
        "GOOGLEDRIVE_COPY_FILE": {
            "id": "file_id",
            "file_id": "file_id",
            "name": "new_title",
            "new_title": "new_title",
            "new_name": "new_title",
        },
        # Google Drive Delete File - GOOGLEDRIVE_DELETE_FOLDER_OR_FILE
        "GOOGLEDRIVE_DELETE_FOLDER_OR_FILE": {"id": "file_id", "file_id": "file_id"},
        # Google Drive Upload File - GOOGLEDRIVE_UPLOAD_FILE
        "GOOGLEDRIVE_UPLOAD_FILE": {
            "file": "file_to_upload",
            "file_to_upload": "file_to_upload",
            "folder": "folder_to_upload_to",
            "folder_to_upload_to": "folder_to_upload_to",
            "parent": "folder_to_upload_to",
            "parent_id": "folder_to_upload_to",
        },
        # Google Drive Add Sharing - GOOGLEDRIVE_ADD_FILE_SHARING_PREFERENCE
        "GOOGLEDRIVE_ADD_FILE_SHARING_PREFERENCE": {
            "id": "file_id",
            "file_id": "file_id",
            "role": "role",
            "type": "type",
            "domain": "domain",
            "email": "email_address",
            "email_address": "email_address",
        },
    },
    # Add google_drive alias (same as googledrive)
    "google_drive": {
        # Google Drive Create File - GOOGLEDRIVE_CREATE_FILE_FROM_TEXT
        "GOOGLEDRIVE_CREATE_FILE_FROM_TEXT": {
            "name": "file_name",
            "file_name": "file_name",
            "filename": "file_name",
            "content": "text_content",
            "text_content": "text_content",
            "text": "text_content",
            "body": "text_content",
            "mime_type": "mime_type",
            "parent": "parent_id",
            "parent_id": "parent_id",
            "folder": "parent_id",
        },
        # Google Drive Create Folder - GOOGLEDRIVE_CREATE_FOLDER
        "GOOGLEDRIVE_CREATE_FOLDER": {
            "name": "folder_name",
            "folder_name": "folder_name",
            "title": "folder_name",
            "parent": "parent_id",
            "parent_id": "parent_id",
            "folder": "parent_id",
        },
        # Google Drive Find File - GOOGLEDRIVE_FIND_FILE
        "GOOGLEDRIVE_FIND_FILE": {
            "name": "name_exact",
            "filename": "name_exact",
            "name_contains": "name_contains",
            "name_not_contains": "name_not_contains",
            "query": "full_text_contains",
            "search": "full_text_contains",
            "full_text_contains": "full_text_contains",
            "full_text_not_contains": "full_text_not_contains",
            "folder": "folder_id",
            "folder_id": "folder_id",
            "parent": "folder_id",
            "mime_type": "mime_type",
            "modified_after": "modified_after",
            "limit": "page_size",
            "max_results": "page_size",
            "count": "page_size",
            "page_size": "page_size",
            "page_token": "page_token",
            "starred": "starred",
            "include_items_from_all_drives": "include_items_from_all_drives",
            "supports_all_drives": "supports_all_drives",
        },
        # Google Drive Find Folder - GOOGLEDRIVE_FIND_FOLDER
        "GOOGLEDRIVE_FIND_FOLDER": {
            "name": "name_exact",
            "folder_name": "name_exact",
            "name_contains": "name_contains",
            "name_not_contains": "name_not_contains",
            "query": "full_text_contains",
            "search": "full_text_contains",
            "full_text_contains": "full_text_contains",
            "full_text_not_contains": "full_text_not_contains",
            "modified_after": "modified_after",
            "starred": "starred",
        },
        # Google Drive Download File - GOOGLEDRIVE_DOWNLOAD_FILE
        "GOOGLEDRIVE_DOWNLOAD_FILE": {
            "id": "file_id",
            "file_id": "file_id",
            "mime_type": "mime_type",
        },
        # Google Drive Edit File - GOOGLEDRIVE_EDIT_FILE
        "GOOGLEDRIVE_EDIT_FILE": {
            "id": "file_id",
            "file_id": "file_id",
            "content": "content",
            "text": "content",
            "body": "content",
            "mime_type": "mime_type",
        },
        # Google Drive Copy File - GOOGLEDRIVE_COPY_FILE
        "GOOGLEDRIVE_COPY_FILE": {
            "id": "file_id",
            "file_id": "file_id",
            "name": "new_title",
            "new_title": "new_title",
            "new_name": "new_title",
        },
        # Google Drive Delete File - GOOGLEDRIVE_DELETE_FOLDER_OR_FILE
        "GOOGLEDRIVE_DELETE_FOLDER_OR_FILE": {"id": "file_id", "file_id": "file_id"},
        # Google Drive Upload File - GOOGLEDRIVE_UPLOAD_FILE
        "GOOGLEDRIVE_UPLOAD_FILE": {
            "file": "file_to_upload",
            "file_to_upload": "file_to_upload",
            "folder": "folder_to_upload_to",
            "folder_to_upload_to": "folder_to_upload_to",
            "parent": "folder_to_upload_to",
            "parent_id": "folder_to_upload_to",
        },
        # Google Drive Add Sharing - GOOGLEDRIVE_ADD_FILE_SHARING_PREFERENCE
        "GOOGLEDRIVE_ADD_FILE_SHARING_PREFERENCE": {
            "id": "file_id",
            "file_id": "file_id",
            "role": "role",
            "type": "type",
            "domain": "domain",
            "email": "email_address",
            "email_address": "email_address",
        },
    },
}

# ============================================================================
# SCHEMA PROCESSORS
# ============================================================================


def create_schema_processor(service_name: str, action: str) -> Callable[[Dict], Dict]:
    """
    Create a schema processor that adds parameter aliases to the schema.
    This helps LLMs understand alternative parameter names.
    """
    mappings = PARAMETER_MAPPINGS.get(service_name, {}).get(action, {})

    def schema_processor(schema: Dict[str, Any]) -> Dict[str, Any]:
        """Add parameter aliases to schema descriptions"""
        try:
            params = schema.get("parameters", {}).get("properties", {})

            # Add aliases to parameter descriptions
            for alias, actual_param in mappings.items():
                if actual_param in params and alias != actual_param:
                    current_desc = params[actual_param].get("description", "")
                    if f"(alias: {alias})" not in current_desc:
                        params[actual_param][
                            "description"
                        ] = f"{current_desc} (alias: {alias})"

            logger.debug(f"Enhanced schema for {service_name}.{action} with aliases")
            return schema

        except Exception as e:
            logger.warning(f"Schema processor error for {service_name}.{action}: {e}")
            return schema

    return schema_processor


# ============================================================================
# PARAMETER PROCESSORS (PRE-EXECUTION)
# ============================================================================


def create_parameter_mapper(service_name: str, action: str) -> Callable[[Dict], Dict]:
    """
    Create a parameter mapper that converts common parameter names
    to Composio's expected format and provides intelligent defaults.
    """
    mappings = PARAMETER_MAPPINGS.get(service_name, {}).get(action, {})

    def parameter_mapper(inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Map common parameter names to Composio's expected format"""
        try:
            mapped_inputs = {}

            for key, value in inputs.items():
                # Use mapping if available, otherwise keep original key
                mapped_key = mappings.get(key, key)
                mapped_inputs[mapped_key] = value

                if mapped_key != key:
                    logger.debug(f"Mapped parameter {key} -> {mapped_key}")

            # Apply intelligent defaults for specific actions
            mapped_inputs = _apply_intelligent_defaults(
                service_name, action, mapped_inputs
            )

            logger.info(
                f"Parameter mapping for {service_name}.{action}: {len(mappings)} mappings applied"
            )
            return mapped_inputs

        except Exception as e:
            logger.error(f"Parameter mapping error for {service_name}.{action}: {e}")
            return inputs  # Return original inputs on error

    return parameter_mapper


def _apply_intelligent_defaults(
    service_name: str, action: str, inputs: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Apply intelligent defaults for common missing parameters.

    This handles cases where users don't provide required parameters
    but we can provide sensible defaults.
    """
    try:
        # Notion-specific intelligent defaults
        if service_name == "notion":
            if action == "NOTION_CREATE_NOTION_PAGE":
                # If no parent_id provided, try to find a reasonable default
                if "parent_id" not in inputs:
                    # Use a well-known workspace root ID if available
                    # This is a common pattern in Notion integrations
                    workspace_root = _get_notion_workspace_root()
                    if workspace_root:
                        inputs["parent_id"] = workspace_root
                        logger.info(f"Applied default parent_id: {workspace_root}")
                    else:
                        # Provide a helpful error message
                        logger.warning(
                            "NOTION_CREATE_NOTION_PAGE requires parent_id. "
                            "Please provide a parent page ID or search for pages first."
                        )

        # Gmail-specific intelligent defaults
        elif service_name == "gmail":
            if action == "GMAIL_SEND_EMAIL":
                # Ensure required fields are present
                if "recipient_email" not in inputs and "to" in inputs:
                    inputs["recipient_email"] = inputs["to"]

        # Add more service-specific defaults as needed

        return inputs

    except Exception as e:
        logger.warning(f"Error applying intelligent defaults: {e}")
        return inputs


def _get_notion_workspace_root() -> Optional[str]:
    """
    Get the workspace root page ID for Notion.

    This could be configured per user or use a common workspace page.
    For now, return None to let the user specify the parent_id.

    Returns:
        Workspace root page ID if available, None otherwise
    """
    # TODO: Implement workspace root detection
    # This could query the user's Notion workspace to find the root page
    # or use a configured default page ID per user
    return None


# ============================================================================
# RESPONSE PROCESSORS (POST-EXECUTION)
# ============================================================================


def create_response_processor(service_name: str, action: str) -> Callable[[Dict], Dict]:
    """
    Create a response processor that cleans and structures Composio responses.
    """

    def response_processor(response: Dict[str, Any]) -> Dict[str, Any]:
        """Process and clean Composio response"""
        try:
            # Handle Composio's standard response format
            if isinstance(response, dict):
                # Extract the actual data from Composio's response wrapper
                if "data" in response:
                    actual_data = response["data"]

                    # Create a clean response structure
                    processed_response = {
                        "success": response.get(
                            "successful", response.get("successfull", True)
                        ),
                        "data": actual_data,
                        "service": service_name,
                        "action": action,
                        "timestamp": response.get("timestamp"),
                        "version": response.get("version"),
                    }

                    # Add error information if present
                    if "error" in response and response["error"]:
                        processed_response["success"] = False
                        processed_response["error"] = response["error"]

                    # Add logs if present (for debugging)
                    if "logs" in response:
                        processed_response["logs"] = response["logs"]

                    logger.debug(f"Processed response for {service_name}.{action}")
                    return processed_response

                # If no "data" field, return the response as-is but add metadata
                else:
                    return {
                        "success": True,
                        "data": response,
                        "service": service_name,
                        "action": action,
                    }

            # If response is not a dict, wrap it
            return {
                "success": True,
                "data": response,
                "service": service_name,
                "action": action,
            }

        except Exception as e:
            logger.error(f"Response processing error for {service_name}.{action}: {e}")
            return {
                "success": False,
                "error": f"Response processing failed: {str(e)}",
                "raw_response": response,
                "service": service_name,
                "action": action,
            }

    return response_processor


# ============================================================================
# PROCESSOR FACTORY
# ============================================================================


def create_processors_for_service(
    service_name: str, actions: List[str]
) -> Dict[str, Dict[str, Callable]]:
    """
    Create all processors (schema, pre, post) for a service and its actions.

    Returns a dictionary in the format expected by Composio's get_tools() method:
    {
        "schema": {Action.GMAIL_SEND_EMAIL: schema_processor_func, ...},
        "pre": {Action.GMAIL_SEND_EMAIL: parameter_mapper_func, ...},
        "post": {Action.GMAIL_SEND_EMAIL: response_processor_func, ...}
    }
    """
    processors = {"schema": {}, "pre": {}, "post": {}}

    for action in actions:
        # Create processors for this action
        schema_proc = create_schema_processor(service_name, action)
        param_mapper = create_parameter_mapper(service_name, action)
        response_proc = create_response_processor(service_name, action)

        # Try to get the Action enum
        try:
            if hasattr(Action, action):
                action_enum = getattr(Action, action)
            else:
                # Try common variations
                action_variations = [
                    f"{service_name.upper()}_{action}",
                    f"{service_name.upper()}_{action.upper()}",
                    action.upper(),
                ]

                action_enum = None
                for variation in action_variations:
                    if hasattr(Action, variation):
                        action_enum = getattr(Action, variation)
                        break

                if action_enum is None:
                    logger.warning(f"Could not find Action enum for {action}")
                    continue

            # Add processors for this action
            processors["schema"][action_enum] = schema_proc
            processors["pre"][action_enum] = param_mapper
            processors["post"][action_enum] = response_proc

            logger.debug(f"Created processors for {service_name}.{action}")

        except Exception as e:
            logger.error(
                f"Failed to create processors for {service_name}.{action}: {e}"
            )
            continue

    return processors


# ============================================================================
# CONVENIENCE FUNCTIONS
# ============================================================================


def get_gmail_processors() -> Dict[str, Dict[str, Callable]]:
    """Get all processors for Gmail actions"""
    gmail_actions = [
        "GMAIL_SEND_EMAIL",
        "GMAIL_CREATE_EMAIL_DRAFT",
        "GMAIL_FETCH_EMAILS",
        "GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID",
        "GMAIL_REPLY_TO_THREAD",
        "GMAIL_DELETE_MESSAGE",
        "GMAIL_MOVE_TO_TRASH",
        "GMAIL_LIST_DRAFTS",
        "GMAIL_DELETE_DRAFT",
        "GMAIL_ADD_LABEL_TO_EMAIL",
        "GMAIL_CREATE_LABEL",
        "GMAIL_LIST_LABELS",
        "GMAIL_REMOVE_LABEL",
        "GMAIL_GET_PROFILE",
        "GMAIL_GET_CONTACTS",
        "GMAIL_SEARCH_PEOPLE",
    ]
    return create_processors_for_service("gmail", gmail_actions)


def get_notion_processors() -> Dict[str, Dict[str, Callable]]:
    """Get all processors for Notion actions"""
    notion_actions = [
        "NOTION_CREATE_NOTION_PAGE",
        "NOTION_SEARCH_NOTION_PAGE",
        "NOTION_FETCH_ROW",
        "NOTION_ADD_PAGE_CONTENT",
        "NOTION_APPEND_BLOCK_CHILDREN",
        "NOTION_CREATE_DATABASE",
        "NOTION_QUERY_DATABASE",
        "NOTION_INSERT_ROW_DATABASE",
        "NOTION_UPDATE_ROW_DATABASE",
        "NOTION_FETCH_DATABASE",
        "NOTION_ARCHIVE_NOTION_PAGE",
        "NOTION_DELETE_BLOCK",
        "NOTION_FETCH_NOTION_BLOCK",
        "NOTION_FETCH_NOTION_CHILD_BLOCK",
        "NOTION_CREATE_COMMENT",
        "NOTION_FETCH_COMMENTS",
        "NOTION_DUPLICATE_PAGE",
        "NOTION_NOTION_UPDATE_BLOCK",
        "NOTION_UPDATE_SCHEMA_DATABASE",
        "NOTION_GET_ABOUT_ME",
        "NOTION_GET_ABOUT_USER",
        "NOTION_LIST_USERS",
        "NOTION_GET_PAGE_PROPERTY_ACTION",
    ]
    return create_processors_for_service("notion", notion_actions)


def get_slack_processors() -> Dict[str, Dict[str, Callable]]:
    """Get all processors for Slack actions"""
    slack_actions = [
        "SLACK_CHAT_POST_MESSAGE",
        "SLACK_ADD_REACTION_TO_AN_ITEM",
        "SLACK_CREATE_A_REMINDER",
        "SLACK_DELETES_A_MESSAGE_FROM_A_CHAT",
        "SLACK_CREATE_CHANNEL_BASED_CONVERSATION",
        "SLACK_ARCHIVE_A_SLACK_CONVERSATION",
        "SLACK_CONVERSATIONS_LIST",
        "SLACK_CONVERSATIONS_INFO",
        "SLACK_CONVERSATIONS_HISTORY",
    ]
    return create_processors_for_service("slack", slack_actions)


def get_googledrive_processors() -> Dict[str, Dict[str, Callable]]:
    """Get all processors for Google Drive actions"""
    googledrive_actions = [
        "GOOGLEDRIVE_CREATE_FILE_FROM_TEXT",
        "GOOGLEDRIVE_CREATE_FOLDER",
        "GOOGLEDRIVE_FIND_FILE",
        "GOOGLEDRIVE_FIND_FOLDER",
        "GOOGLEDRIVE_DOWNLOAD_FILE",
        "GOOGLEDRIVE_EDIT_FILE",
        "GOOGLEDRIVE_COPY_FILE",
        "GOOGLEDRIVE_DELETE_FOLDER_OR_FILE",
        "GOOGLEDRIVE_UPLOAD_FILE",
        "GOOGLEDRIVE_ADD_FILE_SHARING_PREFERENCE",
    ]
    return create_processors_for_service("googledrive", googledrive_actions)


def get_all_processors() -> Dict[str, Dict[str, Callable]]:
    """Get processors for all supported services"""
    gmail_procs = get_gmail_processors()
    notion_procs = get_notion_processors()
    slack_procs = get_slack_processors()
    googledrive_procs = get_googledrive_processors()

    # Merge all processors
    all_processors = {"schema": {}, "pre": {}, "post": {}}

    for proc_type in ["schema", "pre", "post"]:
        all_processors[proc_type].update(gmail_procs[proc_type])
        all_processors[proc_type].update(notion_procs[proc_type])
        all_processors[proc_type].update(slack_procs[proc_type])
        all_processors[proc_type].update(googledrive_procs[proc_type])

    return all_processors
