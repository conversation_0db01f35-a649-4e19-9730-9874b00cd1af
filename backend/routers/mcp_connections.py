import os
from fastapi import APIRouter, HTTPException, Depends, Query, Request
from fastapi.responses import RedirectResponse
from typing import List, Optional
from pydantic import BaseModel
import logging

from services.composio_auth_service import (
    ComposioAuthService,
    UserConnection,
    ConnectionRequest,
    ConnectionStatus,
    ServiceIntegration,
)
from utils.auth_utils import get_current_user_id_from_jwt
from config.composio_integrations import get_integration

logger = logging.getLogger(__name__)


# Request/Response models
class InitiateConnectionRequest(BaseModel):
    service_name: str  # 'gmail', 'googledrive', 'notion', etc.


class InitiateConnectionResponse(BaseModel):
    redirect_url: str
    state: str
    entity_id: str


class ConnectionListResponse(BaseModel):
    connections: List[UserConnection]


class ServiceListResponse(BaseModel):
    services: List[ServiceIntegration]


class ServiceMCPUrlResponse(BaseModel):
    mcp_url: Optional[str]
    service_name: str
    connected: bool


class ConnectionStatusResponse(BaseModel):
    status: str
    connected: bool
    service_name: str
    mcp_url: Optional[str] = None
    connection_id: Optional[str] = None
    error: Optional[str] = None


class WaitActivationRequest(BaseModel):
    state: str
    timeout: int = 120


# Router setup
router = APIRouter(prefix="/api/v1/connections", tags=["mcp-connections"])

# Service initialization
COMPOSIO_API_KEY = os.getenv("COMPOSIO_API_KEY")
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not COMPOSIO_API_KEY:
    raise ValueError("COMPOSIO_API_KEY environment variable is required")
if not SUPABASE_URL or not SUPABASE_SERVICE_KEY:
    raise ValueError("Supabase configuration is required")

composio_service = ComposioAuthService(
    composio_api_key=COMPOSIO_API_KEY,
    supabase_url=SUPABASE_URL,
    supabase_key=SUPABASE_SERVICE_KEY,
)


@router.post("/initiate", response_model=InitiateConnectionResponse)
async def initiate_connection(
    connection_request: InitiateConnectionRequest, request: Request
):
    """
    Initiate OAuth connection for a service using Composio's entity-based approach.

    This endpoint follows the Composio pattern:
    1. Validates the service name against available integrations
    2. Uses the user's ID as the entity_id for Composio
    3. Initiates the OAuth flow and returns a redirect URL
    """
    try:
        # Get user ID from JWT (serves as entity_id in Composio)
        user_id = await get_current_user_id_from_jwt(request)
        logger.info(
            f"🔐 Initiating connection for user {user_id}, service {connection_request.service_name}"
        )

        # Validate service name using the integration config (handles aliases)
        integration = get_integration(connection_request.service_name)
        if not integration:
            # Get available integrations for error message
            available_integrations = await composio_service.get_available_integrations()
            valid_services = [
                integration.service_name for integration in available_integrations
            ]
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported service: {connection_request.service_name}. Available: {valid_services}",
            )

        # Use the canonical service name from the integration
        canonical_service_name = integration.service_name
        logger.info(
            f"✅ Service validated: {connection_request.service_name} -> {canonical_service_name}"
        )

        # Initiate connection using canonical service name
        connection_result = await composio_service.initiate_connection(
            user_id, canonical_service_name
        )

        logger.info(
            f"✅ Connection initiated successfully for {canonical_service_name}"
        )

        return InitiateConnectionResponse(
            redirect_url=connection_result.redirect_url,
            state=connection_result.state,
            entity_id=connection_result.entity_id,
        )

    except ValueError as ve:
        logger.error(f"Validation error: {ve}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"Error in initiate_connection: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to initiate connection: {str(e)}"
        )


@router.post("/wait-activation", response_model=dict)
async def wait_for_activation(
    request: Request, activation_request: WaitActivationRequest
):
    """
    Wait for connection activation using Composio's wait_until_active pattern.

    This endpoint follows Composio's documented approach:
    1. Takes the state from initiate_connection
    2. Uses wait_until_active to poll until connection is ACTIVE
    3. Returns success when connection is ready
    """
    try:
        user_id = await get_current_user_id_from_jwt(request)
        logger.info(
            f"⏳ Waiting for activation - User: {user_id}, State: {activation_request.state}"
        )

        # Use the new wait_for_connection_activation method
        connection = await composio_service.wait_for_connection_activation(
            state=activation_request.state, timeout=activation_request.timeout
        )

        logger.info(f"🎉 Connection activation completed: {connection.id}")

        return {
            "success": True,
            "connection_id": connection.id,
            "service_name": connection.service_name,
            "mcp_url": connection.mcp_url,
            "message": f"Successfully connected to {connection.service_name}",
        }

    except TimeoutError as te:
        logger.error(f"Connection activation timeout: {te}")
        return {
            "success": False,
            "error": "timeout",
            "message": str(te),
        }
    except ValueError as ve:
        logger.error(f"Invalid activation request: {ve}")
        return {
            "success": False,
            "error": "invalid_state",
            "message": str(ve),
        }
    except Exception as e:
        logger.error(f"Error during activation wait: {e}")
        return {
            "success": False,
            "error": "activation_failed",
            "message": f"Failed to activate connection: {str(e)}",
        }


@router.get("/oauth/callback")
async def oauth_callback(
    status: Optional[str] = Query(None),
    connectedAccountId: Optional[str] = Query(None),
    appName: Optional[str] = Query(None),
    error: Optional[str] = Query(None),
    # Keep legacy parameters for backward compatibility
    code: Optional[str] = Query(None),
    state: Optional[str] = Query(None),
):
    """
    Handle OAuth callback from Composio using their actual parameter format.

    Composio sends these parameters:
    - status: 'success' or error status
    - connectedAccountId: The connection ID from Composio
    - appName: The app that was connected
    - error: Error message if any

    This endpoint now handles Composio's actual callback format instead of
    expecting standard OAuth code/state parameters.
    """
    logger.info(
        f"🔄 OAuth callback received - Status: {status}, App: {appName}, ConnectedAccountId: {connectedAccountId}, Error: {error}"
    )

    try:
        # Handle OAuth errors first
        if error:
            logger.error(f"OAuth error received from Composio: {error}")
            return RedirectResponse(
                url=f"/workers/suite/mcp/callback?error={error}",
                status_code=302,
            )

        # Handle Composio's success callback format
        if status == "success" and connectedAccountId and appName:
            logger.info(
                f"✅ Composio OAuth success - App: {appName}, Connection: {connectedAccountId}"
            )

            return RedirectResponse(
                url=f"/workers/suite/mcp/callback?success=true&service={appName.lower()}&connection_id={connectedAccountId}",
                status_code=302,
            )

        # Handle legacy code/state format (if still used)
        if code and state:
            logger.info("🔄 Processing legacy OAuth callback format...")
            try:
                connection = await composio_service.handle_oauth_callback(code, state)
                logger.info(
                    f"🎉 Legacy connection created successfully: {connection.id}"
                )

                return RedirectResponse(
                    url=f"/workers/suite/mcp/callback?success=true&service={connection.service_name}&connection_id={connection.id}",
                    status_code=302,
                )
            except Exception as legacy_error:
                logger.error(f"Legacy OAuth callback failed: {legacy_error}")
                return RedirectResponse(
                    url=f"/workers/suite/mcp/callback?error=Authentication failed. Please try again.",
                    status_code=302,
                )

        # If we get here, the callback format is unexpected
        logger.warning(
            f"Unexpected callback format - Status: {status}, Code: {code}, State: {state}"
        )
        return RedirectResponse(
            url=f"/workers/suite/mcp/callback?error=Unexpected callback format",
            status_code=302,
        )

    except Exception as e:
        logger.error(f"Unexpected error in OAuth callback: {str(e)}")
        return RedirectResponse(
            url=f"/workers/suite/mcp/callback?error=An unexpected error occurred during authentication",
            status_code=302,
        )


@router.get("", response_model=ConnectionListResponse)
async def list_connections(request: Request):
    """
    List all connections for the current user.

    Returns all MCP connections associated with the authenticated user,
    ordered by creation date (most recent first).
    """
    try:
        user_id = await get_current_user_id_from_jwt(request)
        logger.info(f"📋 Listing connections for user {user_id}")

        connections = await composio_service.get_user_connections(user_id)

        logger.info(f"✅ Found {len(connections)} connections for user {user_id}")
        return ConnectionListResponse(connections=connections)

    except Exception as e:
        logger.error(f"Error listing connections: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to list connections: {str(e)}"
        )


@router.get("/{service_name}/mcp-url", response_model=ServiceMCPUrlResponse)
async def get_service_mcp_url(service_name: str, request: Request):
    """
    Get MCP URL for a connected service.

    This endpoint provides the MCP URL for active connections:
    1. Validates service name and handles aliases
    2. Checks for active connection
    3. Returns MCP URL if available
    """
    try:
        user_id = await get_current_user_id_from_jwt(request)
        logger.info(f"🔗 Getting MCP URL for user {user_id}, service {service_name}")

        # Validate service name using the integration config (handles aliases)
        integration = get_integration(service_name)
        if not integration:
            raise HTTPException(
                status_code=400, detail=f"Unsupported service: {service_name}"
            )

        # Use the canonical service name from the integration
        canonical_service_name = integration.service_name
        logger.info(f"✅ Service validated: {service_name} -> {canonical_service_name}")

        # Get connection using canonical service name
        connection = await composio_service.get_connection_by_service(
            user_id, canonical_service_name
        )

        if connection and connection.status == "active":
            logger.info(f"✅ Active connection found for {canonical_service_name}")
            return ServiceMCPUrlResponse(
                mcp_url=connection.mcp_url,
                service_name=canonical_service_name,
                connected=True,
            )
        else:
            logger.info(f"❌ No active connection found for {canonical_service_name}")
            return ServiceMCPUrlResponse(
                mcp_url=None, service_name=canonical_service_name, connected=False
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting MCP URL: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get MCP URL: {str(e)}")


@router.get("/{service_name}/status", response_model=ConnectionStatusResponse)
async def check_connection_status(service_name: str, request: Request):
    """
    Check the status of a connection with enhanced validation.

    This endpoint provides comprehensive connection status checking:
    1. Validates service name and handles aliases
    2. Checks database status
    3. Verifies with Composio if needed
    4. Returns detailed status information
    """
    try:
        user_id = await get_current_user_id_from_jwt(request)
        logger.info(
            f"🔍 Checking connection status for user {user_id}, service {service_name}"
        )

        # Validate service name using the integration config (handles aliases)
        integration = get_integration(service_name)
        if not integration:
            logger.warning(f"❌ Unsupported service: {service_name}")
            return ConnectionStatusResponse(
                status="error",
                connected=False,
                service_name=service_name,
                error=f"Unsupported service: {service_name}",
            )

        # Use the canonical service name from the integration
        canonical_service_name = integration.service_name
        logger.info(f"✅ Service validated: {service_name} -> {canonical_service_name}")

        # Check connection status using canonical service name
        status = await composio_service.check_connection_status(
            user_id, canonical_service_name
        )

        logger.info(
            f"📊 Connection status for {canonical_service_name}: {status.status}"
        )

        return ConnectionStatusResponse(
            status=status.status,
            connected=status.connected,
            service_name=status.service_name,
            mcp_url=status.mcp_url,
            connection_id=status.connection_id,
            error=status.error,
        )

    except Exception as e:
        logger.error(f"Error checking connection status: {e}")
        return ConnectionStatusResponse(
            status="error", connected=False, service_name=service_name, error=str(e)
        )


@router.delete("/{connection_id}")
async def disconnect_service(connection_id: str, request: Request):
    """
    Disconnect a service for the current user.

    This endpoint safely disconnects a service:
    1. Validates user ownership of the connection
    2. Attempts to disconnect from Composio
    3. Updates local database status
    4. Returns success/failure status
    """
    try:
        user_id = await get_current_user_id_from_jwt(request)
        logger.info(
            f"🔌 Disconnecting service for user {user_id}, connection {connection_id}"
        )

        success = await composio_service.disconnect_service(user_id, connection_id)

        if success:
            logger.info(f"✅ Service disconnected successfully: {connection_id}")
            return {
                "message": "Service disconnected successfully",
                "connection_id": connection_id,
            }
        else:
            logger.warning(f"❌ Failed to disconnect service: {connection_id}")
            raise HTTPException(
                status_code=404, detail="Connection not found or already disconnected"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error disconnecting service: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to disconnect service: {str(e)}"
        )


@router.get("/services/available", response_model=ServiceListResponse)
async def get_available_services():
    """
    Get all available services for connection.

    Returns a list of all services that can be connected through
    the configured Composio integrations.
    """
    try:
        logger.info("📋 Getting available services")

        services = await composio_service.get_available_integrations()

        logger.info(f"✅ Found {len(services)} available services")
        return ServiceListResponse(services=services)

    except Exception as e:
        logger.error(f"Error getting available services: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get available services: {str(e)}"
        )


@router.get("/integrations")
async def get_integrations():
    """
    Get integration details for all configured services.

    This endpoint provides detailed information about all configured
    integrations, useful for frontend display and debugging.
    """
    try:
        logger.info("🔧 Getting integration details")

        integrations = await composio_service.get_available_integrations()

        # Convert to dict format for backward compatibility
        integration_list = [
            {
                "service_name": integration.service_name,
                "display_name": integration.display_name,
                "description": integration.description,
                "category": integration.category,
                "icon_url": integration.icon_url,
                "auth_type": integration.auth_type,
                "available": integration.available,
            }
            for integration in integrations
        ]

        logger.info(f"✅ Returning {len(integration_list)} integrations")
        return {"integrations": integration_list}

    except Exception as e:
        logger.error(f"Error getting integrations: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get integrations: {str(e)}"
        )


@router.post("/cleanup/oauth-states")
async def cleanup_expired_oauth_states():
    """
    Clean up expired OAuth states.

    This endpoint should be called periodically (e.g., by a scheduled job)
    to clean up expired OAuth state data from memory.
    """
    try:
        logger.info("🧹 Cleaning up expired OAuth states")

        await composio_service.cleanup_expired_oauth_states()

        logger.info("✅ OAuth states cleanup completed")
        return {"message": "Expired OAuth states cleaned up successfully"}

    except Exception as e:
        logger.error(f"Error cleaning up OAuth states: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to cleanup OAuth states: {str(e)}"
        )


# Health check endpoint
@router.get("/health")
async def health_check():
    """
    Health check endpoint for the MCP connections service.

    Returns the status of the service and its dependencies.
    """
    try:
        # Basic health check - could be expanded to check Composio API, Supabase, etc.
        return {
            "status": "healthy",
            "service": "mcp-connections",
            "composio_configured": bool(COMPOSIO_API_KEY),
            "supabase_configured": bool(SUPABASE_URL and SUPABASE_SERVICE_KEY),
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")
