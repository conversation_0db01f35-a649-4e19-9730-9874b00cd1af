#!/usr/bin/env python3
"""
Simple test for Composio XML integration
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_basic_imports():
    """Test that all our modules can be imported."""
    print("🔍 Testing basic imports...")
    
    try:
        from services.composio_openai_service import ComposioXMLService
        print("✅ ComposioXMLService imported successfully")
        
        from agent.tools.composio_xml_factory import ComposioXMLToolFactory
        print("✅ ComposioXMLToolFactory imported successfully")
        
        from agent.tools.composio_xml_tool import ComposioXMLTool, GmailXMLTool, NotionXMLTool, GitHubXMLTool
        print("✅ XML tool classes imported successfully")
        
        from config.composio_integrations import get_integration, get_all_integrations
        print("✅ Composio integrations config imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

async def test_service_creation():
    """Test that we can create service instances."""
    print("\n🔧 Testing service creation...")
    
    try:
        from services.composio_openai_service import ComposioXMLService
        
        # Test service creation
        service = ComposioXMLService.from_env()
        print("✅ ComposioXMLService created successfully")
        
        # Test factory creation
        from agent.tools.composio_xml_factory import ComposioXMLToolFactory
        factory = ComposioXMLToolFactory()
        print("✅ ComposioXMLToolFactory created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Service creation error: {e}")
        return False

async def test_xml_tool_schemas():
    """Test XML tool schema generation."""
    print("\n📝 Testing XML tool schemas...")
    
    try:
        from agent.tools.composio_xml_tool import GmailXMLTool, NotionXMLTool, GitHubXMLTool
        from services.composio_openai_service import ComposioXMLService
        
        service = ComposioXMLService.from_env()
        
        # Test Gmail tool
        gmail_tool = GmailXMLTool(
            service_name="gmail",
            user_id="test_user",
            available_actions=["send_email", "read_email"],
            composio_service=service
        )
        print("✅ Gmail XML tool created")
        print(f"   XML tag: {gmail_tool.xml_tag}")
        print(f"   Tool name: {gmail_tool.tool_name}")
        
        # Test Notion tool
        notion_tool = NotionXMLTool(
            service_name="notion",
            user_id="test_user",
            available_actions=["create_page", "update_page"],
            composio_service=service
        )
        print("✅ Notion XML tool created")
        print(f"   XML tag: {notion_tool.xml_tag}")
        print(f"   Tool name: {notion_tool.tool_name}")
        
        # Test GitHub tool
        github_tool = GitHubXMLTool(
            service_name="github",
            user_id="test_user",
            available_actions=["create_issue", "create_pull_request"],
            composio_service=service
        )
        print("✅ GitHub XML tool created")
        print(f"   XML tag: {github_tool.xml_tag}")
        print(f"   Tool name: {github_tool.tool_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ XML tool schema error: {e}")
        return False

async def test_integration_config():
    """Test integration configuration."""
    print("\n⚙️ Testing integration configuration...")
    
    try:
        from config.composio_integrations import get_integration, get_all_integrations, get_available_categories
        
        # Test getting specific integration
        gmail_integration = get_integration("gmail")
        if gmail_integration:
            print(f"✅ Gmail integration: {gmail_integration.display_name}")
        
        # Test getting all integrations
        all_integrations = get_all_integrations()
        print(f"✅ Found {len(all_integrations)} total integrations")
        
        # Test categories
        categories = get_available_categories()
        print(f"✅ Found categories: {', '.join(categories)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration config error: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Simple Composio XML Integration Test\n")
    
    # Run tests
    imports_ok = await test_basic_imports()
    service_ok = await test_service_creation()
    schema_ok = await test_xml_tool_schemas()
    config_ok = await test_integration_config()
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    print(f"Basic Imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"Service Creation: {'✅ PASS' if service_ok else '❌ FAIL'}")
    print(f"XML Tool Schemas: {'✅ PASS' if schema_ok else '❌ FAIL'}")
    print(f"Integration Config: {'✅ PASS' if config_ok else '❌ FAIL'}")
    
    all_passed = imports_ok and service_ok and schema_ok and config_ok
    print(f"\nOverall Result: {'🎉 ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎯 Implementation is working correctly!")
        print("✅ All core components are functional")
        print("✅ XML tools can be created and configured")
        print("✅ Integration configuration is loaded")
        print("\n📋 Next steps:")
        print("1. Connect a service via your frontend")
        print("2. Test with a real user_id")
        print("3. Run the agent with user_id parameter")
    else:
        print("\n🔧 Some components need attention")

if __name__ == "__main__":
    asyncio.run(main())
