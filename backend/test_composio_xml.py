#!/usr/bin/env python3
"""
Test script for Composio XML integration

This script tests the new ComposioXMLService and ComposioXMLToolFactory
to ensure they work correctly with user connections.
"""

import asyncio
import os
import sys
import logging
from dotenv import load_dotenv

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

# Set up logging with detailed format for debugging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(), logging.FileHandler("composio_xml_test.log")],
)
logger = logging.getLogger(__name__)


async def test_composio_xml_service():
    """Test the ComposioXMLService functionality."""
    try:
        from services.composio_openai_service import ComposioXMLService

        print("🧪 Testing ComposioXMLService...")

        # Create service instance
        service = ComposioXMLService.from_env()
        print("✅ ComposioXMLService created successfully")

        # Test with a sample user ID (you can replace this with a real user ID from your database)
        test_user_id = "test_user_123"

        # Test getting active services
        print(f"\n📋 Testing get_user_active_services for user: {test_user_id}")
        active_services = await service.get_user_active_services(test_user_id)
        print(f"Active services: {active_services}")

        # Test getting available tools
        print(f"\n🔧 Testing get_user_available_tools for user: {test_user_id}")
        available_tools = await service.get_user_available_tools(test_user_id)
        print(f"Available tools: {len(available_tools)} services")

        for tool_info in available_tools:
            print(f"  - {tool_info['service_name']}: {tool_info['tool_count']} actions")

        return True

    except Exception as e:
        print(f"❌ Error testing ComposioXMLService: {e}")
        return False


async def test_composio_xml_factory():
    """Test the ComposioXMLToolFactory functionality."""
    try:
        from agent.tools.composio_xml_factory import ComposioXMLToolFactory

        print("\n🏭 Testing ComposioXMLToolFactory...")

        # Create factory instance
        factory = ComposioXMLToolFactory()
        print("✅ ComposioXMLToolFactory created successfully")

        # Test supported services
        supported_services = factory.get_supported_services()
        print(f"Supported services: {supported_services}")

        # Test with a sample user ID
        test_user_id = "test_user_123"

        # Test getting tool info
        print(f"\n📊 Testing get_user_tool_info for user: {test_user_id}")
        tool_info = await factory.get_user_tool_info(test_user_id)
        print(f"Tool info: {tool_info}")

        # Test creating user tools
        print(f"\n🔨 Testing create_user_tools for user: {test_user_id}")
        user_tools = await factory.create_user_tools(test_user_id)
        print(f"Created {len(user_tools)} XML tools")

        for tool in user_tools:
            print(
                f"  - {tool.service_name} tool: {len(tool.available_actions)} actions"
            )

        return True

    except Exception as e:
        print(f"❌ Error testing ComposioXMLToolFactory: {e}")
        return False


async def test_xml_tool_schemas():
    """Test XML tool schema generation."""
    try:
        from agent.tools.composio_xml_tool import (
            GmailXMLTool,
            NotionXMLTool,
            GitHubXMLTool,
        )
        from services.composio_openai_service import ComposioXMLService

        print("\n📝 Testing XML tool schemas...")

        # Create a mock service
        service = ComposioXMLService.from_env()

        # Test Gmail tool schema
        gmail_tool = GmailXMLTool(
            service_name="gmail",
            user_id="test_user",
            available_actions=["send_email", "read_email"],
            composio_service=service,
        )
        print("✅ Gmail XML tool created")
        print(f"Gmail schema preview:\n{gmail_tool.xml_schema[:200]}...")

        # Test Notion tool schema
        notion_tool = NotionXMLTool(
            service_name="notion",
            user_id="test_user",
            available_actions=["create_page", "update_page"],
            composio_service=service,
        )
        print("✅ Notion XML tool created")
        print(f"Notion schema preview:\n{notion_tool.xml_schema[:200]}...")

        # Test GitHub tool schema
        github_tool = GitHubXMLTool(
            service_name="github",
            user_id="test_user",
            available_actions=["create_issue", "create_pull_request"],
            composio_service=service,
        )
        print("✅ GitHub XML tool created")
        print(f"GitHub schema preview:\n{github_tool.xml_schema[:200]}...")

        return True

    except Exception as e:
        print(f"❌ Error testing XML tool schemas: {e}")
        return False


async def test_environment_setup():
    """Test that all required environment variables are set."""
    print("🔍 Testing environment setup...")

    required_vars = ["COMPOSIO_API_KEY", "SUPABASE_URL", "SUPABASE_SERVICE_ROLE_KEY"]

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        return False
    else:
        print("✅ All required environment variables are set")
        return True


async def test_logging_integration():
    """Test that logging is working correctly throughout the system."""
    print("\n🔍 Testing logging integration...")

    try:
        # Test that our loggers are configured correctly
        from services.composio_openai_service import logger as service_logger
        from agent.tools.composio_xml_factory import logger as factory_logger
        from agent.tools.composio_xml_tool import logger as tool_logger

        # Test log levels
        service_logger.debug("🧪 Test debug message from ComposioXMLService")
        service_logger.info("🧪 Test info message from ComposioXMLService")
        service_logger.warning("🧪 Test warning message from ComposioXMLService")

        factory_logger.debug("🧪 Test debug message from ComposioXMLToolFactory")
        factory_logger.info("🧪 Test info message from ComposioXMLToolFactory")

        tool_logger.debug("🧪 Test debug message from ComposioXMLTool")
        tool_logger.info("🧪 Test info message from ComposioXMLTool")

        print("✅ Logging integration test completed")
        print("📝 Check 'composio_xml_test.log' file for detailed logs")

        return True

    except Exception as e:
        print(f"❌ Error testing logging integration: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting Composio XML Integration Tests\n")

    # Test environment setup
    env_ok = await test_environment_setup()
    if not env_ok:
        print("\n❌ Environment setup failed. Please check your .env file.")
        return

    # Test logging integration
    logging_ok = await test_logging_integration()

    # Test ComposioXMLService
    service_ok = await test_composio_xml_service()

    # Test ComposioXMLToolFactory
    factory_ok = await test_composio_xml_factory()

    # Test XML tool schemas
    schema_ok = await test_xml_tool_schemas()

    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    print(f"Environment Setup: {'✅ PASS' if env_ok else '❌ FAIL'}")
    print(f"Logging Integration: {'✅ PASS' if logging_ok else '❌ FAIL'}")
    print(f"ComposioXMLService: {'✅ PASS' if service_ok else '❌ FAIL'}")
    print(f"ComposioXMLToolFactory: {'✅ PASS' if factory_ok else '❌ FAIL'}")
    print(f"XML Tool Schemas: {'✅ PASS' if schema_ok else '❌ FAIL'}")

    all_passed = env_ok and logging_ok and service_ok and factory_ok and schema_ok
    print(
        f"\nOverall Result: {'🎉 ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}"
    )

    if all_passed:
        print("\n🎯 Next Steps:")
        print("1. Connect a service (Gmail, Notion, or GitHub) via your frontend")
        print("2. Test with a real user_id that has active connections")
        print("3. Run the agent with user_id parameter to see XML tools in action")
        print("4. Monitor logs in 'composio_xml_test.log' for debugging")
    else:
        print("\n🔧 Please fix the failing tests before proceeding.")
        print("📝 Check 'composio_xml_test.log' for detailed error logs")


if __name__ == "__main__":
    asyncio.run(main())
