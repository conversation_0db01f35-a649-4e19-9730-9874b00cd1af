---
title: Notion
subtitle: Learn how to use Notion with Composio
---

## Overview

### Enum

`NOTION`

### Description

Notion centralizes notes, docs, wikis, and tasks in a unified workspace, letting teams build custom workflows for collaboration and knowledge management

### Authentication Details

<Accordion title="OAUTH2">
<ParamField path="client_id" type="string" required={true}>
</ParamField>

<ParamField path="client_secret" type="string" required={true}>
</ParamField>

<ParamField path="oauth_redirect_uri" type="string" default="https://backend.composio.dev/api/v1/auth-apps/add">
</ParamField>

<ParamField path="scopes" type="string">
</ParamField>

</Accordion>

<Accordion title="API_KEY">
<ParamField path="api_key" type="string" required={true}>
</ParamField>

</Accordion>

## Actions

<AccordionGroup>
<Accordion title="NOTION_ADD_PAGE_CONTENT">
Adds a single content block to a notion page. multiple calls needed for multiple blocks. note: only supports adding to notion pages. blocks that can contain children: - page (any block type) - toggle (any nested content) - to-do (nested to-dos/blocks) - bulleted list (nested lists/blocks) - numbered list (nested lists/blocks) - callout (child blocks) - quote (nested blocks)

**Action Parameters**

<ParamField path="after" type="string">
</ParamField>

<ParamField path="content_block" type="object" required={true}>
</ParamField>

<ParamField path="parent_block_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_APPEND_BLOCK_CHILDREN">
This tool allows appending new child blocks to an existing block in notion. it specifically handles appending blocks to an existing parent block rather than creating new page content. the endpoint uses a patch request to `/v1/blocks/`{block_id}`/children` and facilitates adding new content to existing blocks, extending lists or toggle blocks, inserting new blocks between existing content, and building nested block structures.

**Action Parameters**

<ParamField path="after" type="string">
</ParamField>

<ParamField path="block_id" type="string" required={true}>
</ParamField>

<ParamField path="children" type="array" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_ARCHIVE_NOTION_PAGE">
Archive or unarchive a page in notion.

**Action Parameters**

<ParamField path="archive" type="boolean" default="True">
</ParamField>

<ParamField path="page_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_CREATE_COMMENT">
Create a comment on a page in notion. there are two locations where a new comment can be added (via the public api). to add a new comment to a 1. page, a parent object with a page id must be provided in the body params. 2. existing discussion thread, a discussion id string must be provided in the body params. (inline comments to start a new discussion thread cannot be created via the public api.) either the parent.page id or discussion id parameter must be provided — not both.

**Action Parameters**

<ParamField path="comment" type="object" required={true}>
</ParamField>

<ParamField path="discussion_id" type="string">
</ParamField>

<ParamField path="parent_page_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_CREATE_DATABASE">
Creates a database as a subpage in the specified parent page, with the specified properties schema/columns. currently, the parent of a new database must be a notion page. you cannot update the schema of an existing database using this action. use update schema database action to update the schema or add/remove columns. only use this to create a new database. the title will be automatically converted to notion's rich text format internally.

**Action Parameters**

<ParamField path="parent_id" type="string" required={true}>
</ParamField>

<ParamField path="properties" type="array" required={true}>
</ParamField>

<ParamField path="title" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_CREATE_NOTION_PAGE">
Create a page in notion under page with id parent id. * if a specific parent id is given, directly create the page under that parent. * if no parent id is given, search for all the pages in notion workspace and find the one which is appropriate and select it as parent page. the parent id required is a unique uuid representing the parent page id which can be found notion search notion page tool. this is a new agent format for adding content to a notion page. example of a new format is `{ `parent id`: `59833787-2cf9-4fdf-8782-e53db20768a5`, `title`: `my new report`, `icon`: `😻`, `cover`: `https://google.com/image.png`,_}` don't use the old format, it is not compatible with agents

**Action Parameters**

<ParamField path="cover" type="string">
</ParamField>

<ParamField path="icon" type="string">
</ParamField>

<ParamField path="parent_id" type="string" required={true}>
</ParamField>

<ParamField path="title" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_DELETE_BLOCK">
Sets a block object, including page blocks, to archived: true using the id specified. this can be used to delete a block, page, or database. note: in the notion ui application, this moves the block to the "trash" where it can still be accessed and restored. to restore the block with the api, use the update a block or update page respectively.

**Action Parameters**

<ParamField path="block_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_DUPLICATE_PAGE">
Duplicates a notion page with all its content and properties. this action creates an exact copy of an existing notion page, including all its content, properties, and nested blocks. the duplicate can be created in the same or a different parent location.

**Action Parameters**

<ParamField path="page_id" type="string" required={true}>
</ParamField>

<ParamField path="parent_id" type="string" required={true}>
</ParamField>

<ParamField path="title" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_FETCH_COMMENTS">
Retrieves a list of un-resolved comment objects from a page or block.

**Action Parameters**

<ParamField path="block_id" type="string" required={true}>
</ParamField>

<ParamField path="page_size" type="integer" default="100">
</ParamField>

<ParamField path="start_cursor" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_FETCH_DATABASE">
Retrieves a database object — information that describes the structure and columns of a database — for a provided database id. the response adheres to any limits to an integration’s capabilities. to fetch database rows rather than columns, use the query a database endpoint.

**Action Parameters**

<ParamField path="database_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_FETCH_NOTION_BLOCK">
Retrieves a block object using the id specified. if the block returned contains the key has children: true, use the retrieve block children endpoint to get the list of children. to retrieve page content for a specific page, use retrieve block children and set the page id as the block id.

**Action Parameters**

<ParamField path="block_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_FETCH_NOTION_CHILD_BLOCK">
Returns a paginated array of child block objects contained in the block using the id specified. page content is represented by block childrens. use this function to get complete content of page. returns only the first level of children for the specified block. the response may contain fewer than page size of results. this endpoint requires an integration to have read content capabilities.

**Action Parameters**

<ParamField path="block_id" type="string" required={true}>
</ParamField>

<ParamField path="page_size" type="integer">
</ParamField>

<ParamField path="start_cursor" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_FETCH_ROW">
Each row in database is a page in notion so page id & row id is a uuid of that page. id of the notion page to fetch. to fetch content of a page, "fetch block children" action can be used.

**Action Parameters**

<ParamField path="page_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_GET_ABOUT_ME">
Gets 1. the user id associated with the notion integration. 2. the information about notion account like name of organisation. to get more details about user, you can use the user id to get user details

**Action Parameters**

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_GET_ABOUT_USER">
Get information about the user account using the user id

**Action Parameters**

<ParamField path="user_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_GET_PAGE_PROPERTY_ACTION">
Action to retrieve a specific property value from a notion page.

**Action Parameters**

<ParamField path="page_id" type="string" required={true}>
</ParamField>

<ParamField path="page_size" type="integer">
</ParamField>

<ParamField path="property_id" type="string" required={true}>
</ParamField>

<ParamField path="start_cursor" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_INSERT_ROW_DATABASE">
Each row in the database is a new page in notion. inserting a row in the database creates a page in notion, and includes extra properties as a structured list of key-value pairs for all columns in the database. this action locates the correct database id in the notion workspace and inserts a new page. for each property type, the expected format is: - title, rich text: "text" (e.g., "hello world") (important: max 2000 characters, longer text will be truncated) - number: number (e.g., 23.4) - select: select (e.g., "india") - multi select: array of strings (e.g., "india,usa") - date: iso 8601 format (e.g., "2021-05-11t11:00:00.000-04:00") - people: array of user ids (e.g., "123,456") - url: a url - files: array of urls - checkbox: true or false

**Action Parameters**

<ParamField path="child_blocks" type="array">
</ParamField>

<ParamField path="cover" type="string">
</ParamField>

<ParamField path="database_id" type="string" required={true}>
</ParamField>

<ParamField path="icon" type="string">
</ParamField>

<ParamField path="properties" type="array">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_LIST_USERS">
List all users returns a paginated list of users for the workspace. the response may contain fewer than page size of results. guests are not included in the response.

**Action Parameters**

<ParamField path="page_size" type="integer" default="30">
</ParamField>

<ParamField path="start_cursor" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_NOTION_UPDATE_BLOCK">
Updates an existing block within a notion page. this action supports modifying various block types such as paragraphs, headings, list items, toggles, and to-do items using the patch /v1/blocks/`{block_id}` endpoint.

**Action Parameters**

<ParamField path="additional_properties" type="object">
</ParamField>

<ParamField path="block_id" type="string" required={true}>
</ParamField>

<ParamField path="block_type" type="string" required={true}>
</ParamField>

<ParamField path="content" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_QUERY_DATABASE">
Get list of rows from a notion database filtered and sorted. each row in notion database is represented as a page. each column in the database is represented as a property. to use sorting, filtering find all the properties in the database by using the fetch database action. the response may contain fewer than page size of results and supports pagination.

**Action Parameters**

<ParamField path="database_id" type="string" required={true}>
</ParamField>

<ParamField path="page_size" type="integer" default="2">
</ParamField>

<ParamField path="sorts" type="array">
</ParamField>

<ParamField path="start_cursor" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_SEARCH_NOTION_PAGE">
Search a page in notion. keep the value of parameter query "" to get list of all pages and their ids. this api can be used to search for all available pages currently to get their page ids. it can also be used to find the relevant page id with a specific title or content. if you want to create a page and need all possible parent page ids, then use this api to fetch it. if the response is empty, do a search with empty query to get list of all pages that have been given access to.

**Action Parameters**

<ParamField path="direction" type="string">
</ParamField>

<ParamField path="filter_property" type="string" default="object">
</ParamField>

<ParamField path="filter_value" type="string" default="page">
</ParamField>

<ParamField path="page_size" type="integer" default="2">
</ParamField>

<ParamField path="query" type="string">
</ParamField>

<ParamField path="start_cursor" type="string">
</ParamField>

<ParamField path="timestamp" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_UPDATE_ROW_DATABASE">
Each row in the database is a new page in notion. so updating a row in the database is the same as updating a property of a page in notion. this action updates a specific value in a row in the notion database. for different column/property types --&gt; value should be -title,rich text - text ex. "hello world" -number - number ex. 23.4 -select - select ex. "india" -multi select - multi select comma separated values ex. "india,usa" -date - format ex. "2021-05-11t11:00:00.000-04:00", -people - comma separated ids of people ex. "123,456" -url - a url. -files - comma separated urls -checkbox - "true" or "false"

**Action Parameters**

<ParamField path="cover" type="string">
</ParamField>

<ParamField path="delete_row" type="boolean">
</ParamField>

<ParamField path="icon" type="string">
</ParamField>

<ParamField path="properties" type="array">
</ParamField>

<ParamField path="row_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="NOTION_UPDATE_SCHEMA_DATABASE">
Update a database schema in notion. using this you can change the columns/properties of a database.

**Action Parameters**

<ParamField path="database_id" type="string" required={true}>
</ParamField>

<ParamField path="description" type="string">
</ParamField>

<ParamField path="properties" type="array">
</ParamField>

<ParamField path="title" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

</AccordionGroup>
