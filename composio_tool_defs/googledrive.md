---
title: Googledrive
subtitle: Learn how to use Googledrive with Composio
---

## Overview

### Enum

`GOOGLEDRIVE`

### Description

Connect to Google Drive!

### Authentication Details

<Accordion title="OAUTH2">
<ParamField path="client_id" type="string" required={true}>
</ParamField>

<ParamField path="client_secret" type="string" required={true}>
</ParamField>

<ParamField path="oauth_redirect_uri" type="string" default="https://backend.composio.dev/api/v1/auth-apps/add">
</ParamField>

<ParamField path="scopes" type="string" default="https://www.googleapis.com/auth/drive.file,https://www.googleapis.com/auth/userinfo.email">
</ParamField>

</Accordion>

<Accordion title="BEARER_TOKEN">
<ParamField path="token" type="string" required={true}>
</ParamField>

</Accordion>

## Actions

<AccordionGroup>
<Accordion title="GOOGLEDRIVE_ADD_FILE_SHARING_PREFERENCE">
Add file sharing preferences, including domain-level permissions requiring a domain field.

**Action Parameters**

<ParamField path="domain" type="string">
</ParamField>

<ParamField path="email_address" type="string">
</ParamField>

<ParamField path="file_id" type="string" required={true}>
</ParamField>

<ParamField path="role" type="string" required={true}>
</ParamField>

<ParamField path="type" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GOOGLEDRIVE_COPY_FILE">
Copy a file

**Action Parameters**

<ParamField path="file_id" type="string" required={true}>
</ParamField>

<ParamField path="new_title" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GOOGLEDRIVE_CREATE_FILE_FROM_TEXT">
Create a file from text. max 10mb in size.

**Action Parameters**

<ParamField path="file_name" type="string" required={true}>
</ParamField>

<ParamField path="mime_type" type="string" default="text/plain">
</ParamField>

<ParamField path="parent_id" type="string">
</ParamField>

<ParamField path="text_content" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GOOGLEDRIVE_CREATE_FOLDER">
Create a new folder in google drive. if parent id is provided, it must be a valid google drive folder id. the folder will be created in the root directory if no parent id is specified.

**Action Parameters**

<ParamField path="folder_name" type="string" required={true}>
</ParamField>

<ParamField path="parent_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GOOGLEDRIVE_DELETE_FOLDER_OR_FILE">
Delete folder or file

**Action Parameters**

<ParamField path="file_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GOOGLEDRIVE_DOWNLOAD_FILE">
Download a file.

**Action Parameters**

<ParamField path="file_id" type="string" required={true}>
</ParamField>

<ParamField path="mime_type" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GOOGLEDRIVE_EDIT_FILE">
Edit a file. max 10mb in size.

**Action Parameters**

<ParamField path="content" type="string" required={true}>
</ParamField>

<ParamField path="file_id" type="string" required={true}>
</ParamField>

<ParamField path="mime_type" type="string" default="text/plain">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GOOGLEDRIVE_FIND_FILE">
Find files present in google drive. can be used to search for files by name and content.

**Action Parameters**

<ParamField path="folder_id" type="string">
</ParamField>

<ParamField path="full_text_contains" type="string">
</ParamField>

<ParamField path="full_text_not_contains" type="string">
</ParamField>

<ParamField path="include_items_from_all_drives" type="boolean" default="True">
</ParamField>

<ParamField path="mime_type" type="string">
</ParamField>

<ParamField path="modified_after" type="string">
</ParamField>

<ParamField path="name_contains" type="string">
</ParamField>

<ParamField path="name_exact" type="string">
</ParamField>

<ParamField path="name_not_contains" type="string">
</ParamField>

<ParamField path="page_size" type="integer" default="5">
</ParamField>

<ParamField path="page_token" type="string">
</ParamField>

<ParamField path="starred" type="boolean">
</ParamField>

<ParamField path="supports_all_drives" type="boolean" default="True">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GOOGLEDRIVE_FIND_FOLDER">
Find folder

**Action Parameters**

<ParamField path="full_text_contains" type="string">
</ParamField>

<ParamField path="full_text_not_contains" type="string">
</ParamField>

<ParamField path="modified_after" type="string">
</ParamField>

<ParamField path="name_contains" type="string">
</ParamField>

<ParamField path="name_exact" type="string">
</ParamField>

<ParamField path="name_not_contains" type="string">
</ParamField>

<ParamField path="starred" type="boolean">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GOOGLEDRIVE_PARSE_FILE">
Parse a file. max 10mb in size. (DEPRECATED use DOWNLOAD_FILE)

**Action Parameters**

<ParamField path="file_id" type="string" required={true}>
</ParamField>

<ParamField path="mime_type" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GOOGLEDRIVE_UPLOAD_FILE">
Upload a file to google drive. max file size is 5mb.

**Action Parameters**

<ParamField path="file_to_upload" type="object" required={true}>
</ParamField>

<ParamField path="folder_to_upload_to" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

</AccordionGroup>
