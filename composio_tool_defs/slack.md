---
title: Slack
subtitle: Learn how to use Slack with Composio
---

## Overview

### Enum

`SLACK`

### Description

Slack is a channel-based messaging platform. With Slack, people can work together more effectively, connect all their software tools and services, and find the information they need to do their best work — all within a secure, enterprise-grade environment.

### Authentication Details

<Accordion title="OAUTH2">
<ParamField path="client_id" type="string" required={true}>
</ParamField>

<ParamField path="client_secret" type="string" required={true}>
</ParamField>

<ParamField path="oauth_redirect_uri" type="string" default="https://backend.composio.dev/api/v1/auth-apps/add">
</ParamField>

<ParamField path="scopes" type="string" default="channels:history,chat:write,team:read,channels:read,users:read,reminders:write,reactions:read,reactions:write">
</ParamField>

<ParamField path="user_scopes" type="string" default="channels:history,chat:write,team:read,channels:read,users:read,reminders:write,reactions:read">
</ParamField>

<ParamField path="verification_token" type="string">
</ParamField>

</Accordion>

<Accordion title="BEARER_TOKEN">
<ParamField path="token" type="string" required={true}>
</ParamField>

<ParamField path="verification_token" type="string">
</ParamField>

</Accordion>

## Actions

<AccordionGroup>
<Accordion title="SLACK_ACTIVATE_OR_MODIFY_DO_NOT_DISTURB_DURATION">
Sets a temporary "do not disturb" (dnd) snooze period for the authenticated user in slack. this endpoint allows you to programmatically pause notifications for a specified duration, helping users maintain focus without interruptions. the snooze period starts immediately upon successful api call and lasts for the number of minutes specified. this is particularly useful for integrations that manage user availability or for creating custom dnd controls within your slack-connected applications. note that this will override any existing dnd settings for the user.

**Action Parameters**

<ParamField path="num_minutes" type="string" required={true}>
</ParamField>

<ParamField path="token" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_ADD_AN_EMOJI_ALIAS_IN_SLACK">
This endpoint allows slack workspace administrators to create an alias for an existing emoji within an enterprise grid organization. it's used to give alternative names to emojis, making them easier to find or use. this is particularly useful for creating shortcuts to frequently used emojis or standardizing emoji names across teams. the alias will function identically to the original emoji. note that this endpoint is only available for enterprise grid customers and requires administrative privileges. it does not create new emojis, but rather creates new ways to reference existing ones.

**Action Parameters**

<ParamField path="alias_for" type="string" required={true}>
</ParamField>

<ParamField path="name" type="string" required={true}>
</ParamField>

<ParamField path="token" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_ADD_A_CUSTOM_EMOJI_TO_A_SLACK_TEAM">
Adds a custom emoji to a slack workspace. this endpoint allows workspace administrators to upload new emojis that can be used by all members in messages and reactions. it requires specifying a unique name for the emoji and providing a url to the image file. the function is useful for personalizing a workspace with brand-specific or team-specific emojis. note that there may be limits on the total number of custom emojis allowed in a workspace, and overuse can impact performance. use this endpoint when you need to programmatically add new emojis to enhance team communication and expression.

**Action Parameters**

<ParamField path="name" type="string" required={true}>
</ParamField>

<ParamField path="token" type="string" required={true}>
</ParamField>

<ParamField path="url" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_ADD_A_REMOTE_FILE_FROM_A_SERVICE">
The 'files.remote.add' endpoint allows you to add a remote file to slack, making it accessible and shareable within the platform. this method is used when you want to integrate external files into slack conversations without uploading the actual file content to slack's servers. it's particularly useful for large files, frequently updated documents, or content that needs to remain hosted externally. the endpoint creates a reference to the external file, allowing slack users to view and interact with it as if it were a native slack file. note that this method only creates a link to the file; it does not duplicate or store the file content on slack's infrastructure.

**Action Parameters**

<ParamField path="external_id" type="string">
</ParamField>

<ParamField path="external_url" type="string">
</ParamField>

<ParamField path="filetype" type="string">
</ParamField>

<ParamField path="indexable_file_contents" type="string">
</ParamField>

<ParamField path="preview_image" type="string">
</ParamField>

<ParamField path="title" type="string">
</ParamField>

<ParamField path="token" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_ADD_A_STAR_TO_AN_ITEM">
The 'stars.add' endpoint allows users to add a star to various items within slack, including channels, files, file comments, or specific messages. this method is used to mark items for later reference or to indicate importance. it's particularly useful for creating personal bookmarks or highlighting significant content within the slack workspace. the endpoint is flexible, allowing users to star different types of content by specifying the appropriate parameters. however, users should be aware that only one type of item (channel, file, file comment, or message) can be starred per api call. this endpoint should be used when users want to programmatically add stars to slack items, which can be helpful for integrations or automating workspace organization.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="file" type="string">
</ParamField>

<ParamField path="file_comment" type="string">
</ParamField>

<ParamField path="timestamp" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_ADD_DEFAULT_CHANNELS_TO_IDP_GROUP">
Adds default channels to a specified user group in a slack workspace. this endpoint allows administrators to automatically include members of a user group in designated channels, streamlining onboarding and ensuring consistent access to relevant information. it's particularly useful for setting up standard channel memberships for different roles or departments within an organization. the endpoint should be used when configuring or updating user group settings, especially after creating a new user group or when organizational changes require adjustments to default channel memberships. note that this operation adds to, rather than replaces, any existing default channels for the user group.

**Action Parameters**

<ParamField path="channel_ids" type="string" required={true}>
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

<ParamField path="usergroup_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_ADD_ENTERPRISE_USER_TO_WORKSPACE">
This endpoint assigns a user to a specific slack workspace with customizable access levels. it allows administrators to add a user to a workspace as a regular member, a guest (restricted), or a single-channel guest (ultra-restricted). the endpoint also provides the option to automatically add the user to specific channels upon assignment. use this when you need to programmatically add users to a workspace with precise control over their access rights and initial channel memberships. it's particularly useful for large-scale user management or when integrating slack with external user management systems. note that this endpoint requires appropriate admin permissions to execute successfully.

**Action Parameters**

<ParamField path="channel_ids" type="string">
</ParamField>

<ParamField path="is_restricted" type="boolean">
</ParamField>

<ParamField path="is_ultra_restricted" type="boolean">
</ParamField>

<ParamField path="team_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_ADD_IDP_GROUP_ALLOWLIST_TO_CHANNEL">
This endpoint adds an identity provider (idp) group to a private channel's allowlist in slack, restricting access to only members of the specified group. it's designed for enterprise grid workspaces to enhance security and access control. use this when you need to limit channel access to specific groups within your organization. the endpoint is particularly useful for creating secure communication spaces for teams or projects with sensitive information. note that this operation only applies to private channels and requires administrative privileges.

**Action Parameters**

<ParamField path="channel_id" type="string" required={true}>
</ParamField>

<ParamField path="group_id" type="string" required={true}>
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

<ParamField path="token" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_ADD_REACTION_TO_AN_ITEM">
Adds a reaction (emoji) to a specific message in a slack channel. this endpoint allows users to interact with messages by adding emojis as reactions, enhancing engagement and communication within slack. it should be used when a user wants to express a quick response or sentiment to a message without sending a new message. the endpoint requires specifying the target channel, the emoji name, and the timestamp of the message to react to. it's important to note that the user must have access to the specified channel and message, and the emoji must be available in the slack workspace. this endpoint does not support removing reactions or retrieving existing reactions on a message.

**Action Parameters**

<ParamField path="channel" type="string" required={true}>
</ParamField>

<ParamField path="name" type="string" required={true}>
</ParamField>

<ParamField path="timestamp" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_APPROVE_AN_APP_INSTALLATION_IN_A_WORKSPACE">
The admin.apps.approve endpoint is used to approve an app installation request in a slack workspace or across an entire enterprise. this method allows administrators to grant permission for a specific app to be installed or used within their slack environment. it is primarily used in enterprise grid workspaces where app installations require admin approval for security and compliance reasons. the endpoint should be used when an administrator needs to review and approve a pending app installation request, either for a single workspace or organization-wide. it requires admin-level permissions and can only be executed by users with appropriate administrative access. this method is crucial for maintaining control over the apps used within a slack organization and ensuring that only approved apps are allowed to integrate with the workspace.

**Action Parameters**

<ParamField path="app_id" type="string">
</ParamField>

<ParamField path="request_id" type="string">
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_APPROVE_WORKSPACE_INVITE_REQUEST">
The admin.inviterequests.approve endpoint allows workspace administrators to approve pending invite requests for their slack workspace. this method is used to grant access to individuals who have requested to join the workspace. it's particularly useful for managing access in controlled or private workspaces where new member additions require explicit approval. the endpoint should be used when an administrator has reviewed and decided to accept an invite request. it's important to note that this action is irreversible, and once approved, the invited user will gain access to the workspace. this endpoint is not for creating new invite requests or for bulk approvals; it processes one invite request at a time.

**Action Parameters**

<ParamField path="invite_request_id" type="string" required={true}>
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_APPS_PERMISSIONS_LIST_PERMISSIONS">
Retrieves detailed information about the permissions granted to a slack application. this endpoint is used to audit and review the current set of permissions associated with an app, helping developers and administrators understand the app's capabilities within a slack workspace. it provides a comprehensive list of all permissions the app has been granted, which is crucial for security audits, troubleshooting, or when preparing to request additional scopes. the endpoint does not modify any permissions; it only returns the current state. note that the permissions returned are specific to the app associated with the authentication token used in the request.(DEPRECATED use list_app_permissions_on_a_team)

**Action Parameters**

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_APPS_PERMISSIONS_SCOPES_GET_LIST">
Retrieves a list of oauth scopes associated with the authenticated slack app. this endpoint allows developers to view the current permissions granted to their app within a slack workspace. it's particularly useful for auditing app permissions, verifying the app's access levels, or preparing for permission changes. the endpoint returns all scopes the app has been granted, which can include both bot and user token scopes. it should be used when you need to review or confirm the current set of permissions for your slack app. note that this endpoint does not modify any permissions; it only provides a read-only view of the existing scopes.(DEPRECATED use list_app_permissions_scopes_on_a_team)

**Action Parameters**

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_ARCHIVE_A_PUBLIC_OR_PRIVATE_CHANNEL">
The admin.conversations.archive endpoint allows workspace administrators to archive a specific slack channel. this action preserves the channel's content in a read-only state, preventing further messages from being posted while retaining historical data. use this endpoint when you need to close a channel that is no longer active or relevant, but want to keep its contents accessible for reference. it's important to note that archiving a channel is reversible; archived channels can be unarchived if needed. however, exercise caution when using this endpoint, as it affects all members of the channel and may impact ongoing conversations or workflows.

**Action Parameters**

<ParamField path="channel_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_ARCHIVE_A_SLACK_CONVERSATION">
The 'conversations.archive' endpoint archives a specified slack conversation, making it unavailable for new messages while preserving its history. this method can be used for both public and private channels, but not all types of conversations can be archived. use this endpoint when you need to remove a conversation from active use without deleting its content. it's particularly useful for maintaining a clean workspace by archiving inactive or completed project channels. note that archiving a conversation may affect integrations or workflows that depend on it, so use with caution. certain types of conversations, such as general channels or those required by app installations, cannot be archived.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_ASSOCIATE_DEFAULT_WORKSPACES_WITH_IDP_GROUP">
This endpoint adds one or more teams (workspaces) to a specified user group within a slack organization. it's used to extend the reach of a user group across multiple workspaces, facilitating organization-wide management of user groups. the endpoint is particularly useful for administrators managing large, multi-workspace organizations who need to ensure consistent group membership across teams. it supports auto-provisioning of accounts for external idp group members, streamlining the process of integrating external users into slack workspaces. note that this operation requires admin-level permissions and can only be performed on teams within the same organization as the api token.

**Action Parameters**

<ParamField path="auto_provision" type="boolean">
</ParamField>

<ParamField path="team_ids" type="string" required={true}>
</ParamField>

<ParamField path="usergroup_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_CHAT_POST_MESSAGE">
The chat.postmessage endpoint allows you to send a message to a slack channel or user. this versatile tool can be used to post simple text messages, create rich interactive messages with blocks or attachments, initiate or reply to threads, and customize the appearance of bot messages. it's ideal for sending notifications, updates, or interactive content to slack users or channels programmatically. the endpoint supports extensive customization of message content and appearance, making it suitable for a wide range of use cases from simple notifications to complex interactive workflows. however, it's important to note that the message structure and appearance may vary depending on the slack client and the parameters used.(DEPRECATED use sends_a_message_to_a_slack_channel)

**Action Parameters**

<ParamField path="as_user" type="boolean">
</ParamField>

<ParamField path="attachments" type="string">
</ParamField>

<ParamField path="blocks" type="string">
</ParamField>

<ParamField path="channel" type="string" required={true}>
</ParamField>

<ParamField path="icon_emoji" type="string">
</ParamField>

<ParamField path="icon_url" type="string">
</ParamField>

<ParamField path="link_names" type="boolean">
</ParamField>

<ParamField path="mrkdwn" type="boolean">
</ParamField>

<ParamField path="parse" type="string">
</ParamField>

<ParamField path="reply_broadcast" type="boolean">
</ParamField>

<ParamField path="text" type="string">
</ParamField>

<ParamField path="thread_ts" type="string">
</ParamField>

<ParamField path="unfurl_links" type="boolean">
</ParamField>

<ParamField path="unfurl_media" type="boolean">
</ParamField>

<ParamField path="username" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_CHECKS_API_CALL_AND_RETURNS_SUCCESS_OR_ERROR">
The api.test endpoint is a simple method used to test the functionality of the slack api. it allows developers to verify that their authentication credentials are working correctly and that they can successfully connect to the slack platform. this endpoint doesn't perform any specific actions within a slack workspace but instead returns a basic response to confirm that the api is operational. it's particularly useful during initial setup, when troubleshooting connectivity issues, or as part of automated health checks to ensure ongoing api availability. the endpoint requires no parameters and typically returns a minimal json response indicating a successful connection.

**Action Parameters**

<ParamField path="error" type="string">
</ParamField>

<ParamField path="foo" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_CHECKS_AUTHENTICATION_AND_IDENTITY">
The auth.test endpoint is used to validate and test the authentication token provided in the request. it verifies the token's validity and returns information about the authenticated user or bot. this endpoint is particularly useful for checking if a token is still valid, identifying the associated user or bot, and determining the scopes granted to the token. it should be used when you need to confirm the authentication status or retrieve basic information about the authenticated entity without making changes to any data. the endpoint does not modify any data or perform any actions beyond authentication verification.

**Action Parameters**

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_CLOSE_DM_OR_MULTI_PERSON_DM">
The 'conversations.close' endpoint allows you to close a conversation in slack. this method is specifically used to close direct messages (dms) or multi-person direct messages (mpdms). when invoked, it removes the specified conversation from the user's sidebar in the slack client, effectively 'closing' it from their view. this endpoint is particularly useful for decluttering a user's slack workspace by removing inactive or less relevant conversations from immediate view. it's important to note that closing a conversation does not delete it; the conversation history is preserved and can be reopened later if needed. this method should be used judiciously, especially in shared channels or group conversations, as it only affects the view for the user making the api call.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_CONVERSATIONS_HISTORY">
Retrieves the message history of a specified slack conversation or channel. this endpoint allows you to fetch a chronological list of messages and events that have occurred within a channel, group, or direct message conversation. it's particularly useful for applications that need to analyze conversation patterns, create chat archives, or display message history to users. the endpoint supports pagination for handling large message histories and allows filtering by time range. it returns detailed message objects including sender information, timestamps, and message content, making it a powerful tool for building slack integrations that require access to conversation data.(DEPRECATED use fetch_conversation_history)

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="inclusive" type="boolean">
</ParamField>

<ParamField path="latest" type="integer">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="oldest" type="integer">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_CONVERSATIONS_INFO">
The conversations.info endpoint retrieves detailed information about a specific conversation in slack. it provides comprehensive data about channels, direct messages, or multi-person direct messages, including their properties, members, and settings. this endpoint is particularly useful when you need to access metadata about a conversation, such as its name, purpose, creation date, or membership details. it should be used when detailed information about a specific slack conversation is required, rather than for listing multiple conversations or accessing message content. note that while this endpoint provides extensive metadata, it does not return the actual messages within the conversation; for that, you would need to use a separate endpoint like conversations.history.(DEPRECATED use retrieve_conversation_information)

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="include_locale" type="boolean">
</ParamField>

<ParamField path="include_num_members" type="boolean">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_CONVERSATIONS_LIST">
Retrieves a list of all conversations in a slack workspace, including public channels, private channels, multi-person direct messages (mpim), and direct messages (im). this endpoint is essential for obtaining an overview of all available conversations, which can be used for workspace analysis, channel management, or as a precursor to performing actions on specific conversations. the method supports filtering by conversation types, exclusion of archived channels, and pagination for handling large workspaces efficiently. use this endpoint when you need to display available channels to users, perform workspace-wide operations, or gather data about the structure of a slack workspace. note that the returned list may be affected by the permissions of the authenticated user, and some conversations might be excluded if the user doesn't have access to them.(DEPRECATED use list_all_slack_team_channels_with_various_filters)

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="exclude_archived" type="boolean">
</ParamField>

<ParamField path="limit" type="integer" default="1">
</ParamField>

<ParamField path="types" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_CONVERT_PUBLIC_CHANNEL_TO_PRIVATE">
Converts a specified public slack channel to a private channel. this endpoint allows slack workspace administrators to change the visibility and accessibility of a channel from public to private. once converted, the channel will only be visible and accessible to invited members, enhancing privacy and control over sensitive conversations. this action is irreversible and should be used cautiously, as it may impact team communication and workflows. it's particularly useful for situations where a previously public channel needs to be restricted due to changes in project scope, team structure, or information sensitivity.

**Action Parameters**

<ParamField path="channel_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_CREATE_AN_ENTERPRISE_TEAM">
Creates a new team within a slack enterprise grid workspace. this endpoint allows administrators to set up a new organizational unit, specifying its domain, name, description, and discoverability settings. it's particularly useful for large organizations that need to create separate teams for different departments, projects, or subsidiaries. the endpoint should be used when expanding the workspace structure or setting up new collaborative spaces within the enterprise grid. note that this operation requires administrative privileges and should be used judiciously to maintain a coherent organizational structure.

**Action Parameters**

<ParamField path="team_description" type="string">
</ParamField>

<ParamField path="team_discoverability" type="string">
</ParamField>

<ParamField path="team_domain" type="string" required={true}>
</ParamField>

<ParamField path="team_name" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_CREATE_A_REMINDER">
The 'reminders.add' endpoint allows you to create new reminders in slack. it's designed to help users set notifications for future tasks, events, or any information they need to remember. this tool is particularly useful for managing personal tasks, setting team reminders, or scheduling follow-ups within slack's collaborative environment. the endpoint offers flexibility in timing, supporting one-time reminders as well as recurring ones through natural language input. while it focuses on creating reminders, it does not provide functionality for listing, modifying, or deleting existing reminders. note that if no specific user is designated, the reminder defaults to the creator, making it versatile for both personal and team use.

**Action Parameters**

<ParamField path="text" type="string" required={true}>
</ParamField>

<ParamField path="time" type="string" required={true}>
</ParamField>

<ParamField path="user" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_CREATE_A_SLACK_USER_GROUP">
Creates a new user group in slack, allowing for organized team communication and management. this endpoint is used to set up a group with a unique name, optional description, default channels, and a mention handle. it's particularly useful for creating functional teams, project groups, or department-wide communication channels. the endpoint allows for immediate configuration of the group's default channels and provides options for including member count information. it should be used when setting up new team structures or reorganizing existing communication channels within slack.

**Action Parameters**

<ParamField path="channels" type="string">
</ParamField>

<ParamField path="description" type="string">
</ParamField>

<ParamField path="handle" type="string">
</ParamField>

<ParamField path="include_count" type="boolean">
</ParamField>

<ParamField path="name" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_CREATE_CHANNEL_BASED_CONVERSATION">
Creates a new public or private channel (conversation) in a slack workspace or organization. this endpoint allows administrators to programmatically set up channels, specifying whether they should be private or public, team-specific or organization-wide. it's particularly useful for automating workspace setup, creating standardized channels across teams, or implementing custom onboarding processes. the endpoint requires specifying the channel name and privacy setting, with options to add a description and determine the channel's scope within the organization.

**Action Parameters**

<ParamField path="description" type="string">
</ParamField>

<ParamField path="is_private" type="boolean" required={true}>
</ParamField>

<ParamField path="name" type="string" required={true}>
</ParamField>

<ParamField path="org_wide" type="boolean">
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_CUSTOMIZE_URL_UNFURLING_IN_MESSAGES">
The 'chat.unfurl' endpoint allows you to customize the unfurling behavior for urls within a specific slack message. unfurling expands urls into rich content previews, enhancing message readability and context. use this endpoint when you want to add custom previews for urls in a message or initiate a user authentication flow for certain unfurls. it's particularly useful for integrating external services or providing additional context for shared links. the endpoint requires specifying the target message using channel id and timestamp, and offers options for custom unfurl content and user authentication processes. note that this endpoint modifies existing messages and should be used judiciously to maintain conversation clarity.

**Action Parameters**

<ParamField path="channel" type="string" required={true}>
</ParamField>

<ParamField path="ts" type="string" required={true}>
</ParamField>

<ParamField path="unfurls" type="string">
</ParamField>

<ParamField path="user_auth_message" type="string">
</ParamField>

<ParamField path="user_auth_required" type="boolean">
</ParamField>

<ParamField path="user_auth_url" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_DELETES_A_MESSAGE_FROM_A_CHAT">
Deletes a specific message from a slack conversation. this endpoint should be used when you need to remove a message from a channel, group, or direct message conversation. it's particularly useful for cleaning up bot messages, correcting mistakes, or managing conversation history. the deletion is permanent and cannot be undone, so use with caution. this method requires either 'chat:write:user' or 'chat:write:bot' scope, depending on whether you're deleting as a user or a bot. note that you can only delete messages that were posted by the authenticated user or bot – you cannot delete other users' messages.

**Action Parameters**

<ParamField path="as_user" type="boolean">
</ParamField>

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="ts" type="integer">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_DELETE_A_COMMENT_ON_A_FILE">
This endpoint allows you to delete a specific comment on a file in a slack workspace. it is used when you need to remove a comment from a file, either for moderation purposes or to clean up outdated or irrelevant feedback. the endpoint requires both the file identifier and the comment identifier to ensure precise targeting of the comment to be deleted. it's important to note that this action is irreversible, so it should be used with caution. this endpoint is particularly useful for maintaining a clean and relevant discussion around shared files in slack.

**Action Parameters**

<ParamField path="file" type="string">
</ParamField>

<ParamField path="id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_DELETE_A_FILE_BY_ID">
The 'files.delete' endpoint allows you to permanently remove a file from a slack workspace. this method is used when you need to delete a file that is no longer needed or to manage storage space in your slack environment. it's important to note that this action is irreversible - once a file is deleted, it cannot be recovered. this endpoint should be used cautiously, ensuring that the file is no longer required before deletion. it's particularly useful for maintaining data hygiene, complying with data retention policies, or removing sensitive information from the workspace. the endpoint requires the id of the file to be deleted and will remove the file along with any associated comments or shares within slack.

**Action Parameters**

<ParamField path="file" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_DELETE_A_PUBLIC_OR_PRIVATE_CHANNEL">
The admin.conversations.delete endpoint allows workspace administrators to permanently delete a channel from a slack enterprise grid organization. this powerful function should be used when a channel is no longer needed and all its contents can be permanently removed. it's important to note that this action is irreversible and will delete all messages and files within the channel. this endpoint is only available for enterprise grid workspaces and requires admin-level permissions. it should be used with caution and typically as part of a careful channel management strategy or data cleanup process.

**Action Parameters**

<ParamField path="channel_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_DELETE_A_SCHEDULED_MESSAGE_IN_A_CHAT">
This endpoint allows you to delete a scheduled message from slack before it is sent. it is used to remove a message that has been queued for future delivery but is no longer needed or contains errors. the endpoint requires the channel id where the message was scheduled and the unique scheduled message id. you can optionally specify whether to delete the message as an authenticated user or a bot. this method is particularly useful for managing and updating automated message schedules or correcting mistakes in scheduled announcements.

**Action Parameters**

<ParamField path="as_user" type="boolean">
</ParamField>

<ParamField path="channel" type="string" required={true}>
</ParamField>

<ParamField path="scheduled_message_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_DELETE_A_SLACK_REMINDER">
The 'reminders.delete' endpoint allows you to remove a specific reminder from a user's list of reminders in slack. this method is used when you need to cancel a previously set reminder, either because the task has been completed or is no longer relevant. it requires the unique id of the reminder to be deleted, which must be obtained from a previous 'reminders.list' or 'reminders.add' operation. the endpoint is particularly useful for maintaining an up-to-date and clutter-free list of reminders for slack users. note that once a reminder is deleted, it cannot be recovered, so use this endpoint with caution.

**Action Parameters**

<ParamField path="reminder" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_DELETE_USER_PROFILE_PHOTO">
The users.deletephoto endpoint allows users to remove their current profile picture on slack. this method is used when a user wants to revert to the default profile image or before uploading a new photo. it's a simple operation that requires no additional parameters beyond authentication. keep in mind that this action is irreversible - once deleted, the previous profile photo cannot be restored. if you need to change the profile photo, consider using the users.setphoto method instead of deleting and re-uploading.

**Action Parameters**

<ParamField path="token" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_DENY_WORKSPACE_INVITE_REQUEST">
This endpoint allows workspace administrators to deny an invite request to join a slack workspace. it is used when an administrator wants to reject a pending invitation request, preventing the requester from joining the workspace. the endpoint should be called when reviewing and managing incoming invite requests, particularly when the request does not meet the criteria for approval. it's important to note that this action is irreversible, so administrators should use it carefully. this endpoint does not provide any information about the invite request itself or the reason for denial; it simply processes the denial action.

**Action Parameters**

<ParamField path="invite_request_id" type="string" required={true}>
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_DISABLE_AN_EXISTING_SLACK_USER_GROUP">
Disables a specified user group in slack. this endpoint is used to deactivate an existing user group, setting its 'date delete' parameter to a non-zero value. it's particularly useful for archiving or temporarily deactivating groups that are no longer needed. the endpoint requires the id of the user group to be disabled and optionally can return the count of users in the group. use this when you need to manage team structures or clean up inactive groups. note that disabling a user group does not delete it permanently, and it can be re-enabled later if needed.

**Action Parameters**

<ParamField path="include_count" type="boolean">
</ParamField>

<ParamField path="usergroup" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_DISCONNECT_CHANNEL_FROM_WORKSPACES">
The admin.conversations.disconnectshared endpoint is used to disconnect a shared slack channel from one or more workspaces. this api method is specifically designed for enterprise grid workspaces and allows administrators to manage slack connect channels and direct messages (dms). it should be used when you need to remove a workspace's access to a shared channel, typically for security, compliance, or organizational restructuring purposes. the endpoint requires administrative permissions and can only disconnect channels that are already shared across workspaces. it's important to note that this action may affect communication and collaboration between teams, so it should be used judiciously and in accordance with your organization's policies.

**Action Parameters**

<ParamField path="channel_id" type="string" required={true}>
</ParamField>

<ParamField path="leaving_team_ids" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_ENABLE_A_SPECIFIED_USER_GROUP">
Enables a previously disabled user group in slack, making it active and usable within the workspace. this endpoint is used to reactivate a user group that was previously deactivated, allowing it to be used for mentions, permissions, and other group-based features in slack. it's particularly useful when you need to temporarily disable a group and then re-enable it later. the endpoint requires the unique identifier of the user group and optionally can return the current number of members in the group. it should not be used to create new user groups or modify existing ones beyond their enabled/disabled status.

**Action Parameters**

<ParamField path="include_count" type="boolean">
</ParamField>

<ParamField path="usergroup" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_ENABLE_PUBLIC_SHARING_OF_A_FILE">
Creates a public url for sharing a file in slack. this endpoint enables users to generate a publicly accessible link for a specific file, allowing it to be shared with people outside the slack workspace. it's useful when you need to provide file access to external collaborators or for public distribution. note that this action may have security implications, so use it cautiously and only for files that are appropriate for public sharing. the endpoint doesn't create a new file; it simply generates a public url for an existing file in the workspace.

**Action Parameters**

<ParamField path="file" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_END_A_CALL_WITH_DURATION_AND_ID">
The 'calls.end' endpoint is used to terminate an ongoing call in slack. this method is part of the slack calls api and allows developers to programmatically end a call that was previously initiated and registered using the 'calls.add' method. it's particularly useful for integrations that manage call workflows or need to automate call termination based on certain conditions. the endpoint requires the unique call id to identify which call to end, and optionally allows specifying the call duration for record-keeping purposes. it should be used when a call needs to be ended programmatically, such as in scheduled meetings or when integrating with external systems that manage call lifecycles.

**Action Parameters**

<ParamField path="duration" type="integer">
</ParamField>

<ParamField path="id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_END_USER_DO_NOT_DISTURB_SESSION">
Ends the "do not disturb" (dnd) status for the authenticated user in slack. this endpoint should be used when a user wants to make themselves available for notifications and messages after a period of uninterrupted focus time. it immediately disables the dnd mode, allowing incoming notifications to be received. this method is particularly useful for integrations that manage a user's availability status automatically based on external factors or schedules. note that this endpoint only affects the dnd status and does not modify other user status settings or preferences in slack.

**Action Parameters**

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_END_USER_SNOOZE_MODE_IMMEDIATELY">
The dnd.endsnooze endpoint immediately ends the current user's do not disturb (dnd) session in slack, effectively canceling the snooze mode. this method should be used when a user wants to resume receiving notifications before their scheduled dnd period ends. it's particularly useful for situations where a user's focus period has concluded earlier than anticipated or when urgent communication is required. the endpoint doesn't require any parameters and operates on the authenticated user's account. it's important to note that this action cannot be undone, and users will need to manually re-enable dnd if needed after using this endpoint.

**Action Parameters**

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_EXCHANGE_OAUTH_CODE_FOR_ACCESS_TOKEN">
The oauth.access endpoint is used to exchange a temporary authorization code for an access token in the slack oauth 2.0 flow. this endpoint should be called after your application has received the authorization code from slack, typically as part of the redirect process after a user has approved your app's requested permissions. the obtained access token is crucial for making authenticated requests to other slack api endpoints on behalf of the user or workspace. this endpoint is a one-time use per authorization code and should be called server-side to keep your client secret secure. it's important to note that this endpoint does not require authentication itself, as it's part of the process to obtain authentication credentials.

**Action Parameters**

<ParamField path="client_id" type="string">
</ParamField>

<ParamField path="client_secret" type="string">
</ParamField>

<ParamField path="code" type="string">
</ParamField>

<ParamField path="redirect_uri" type="string">
</ParamField>

<ParamField path="single_channel" type="boolean">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_EXCHANGE_OAUTH_CODE_FOR_WORKSPACE_TOKEN">
Exchanges a temporary authorization code for an access token in the slack oauth 2.0 flow. this endpoint is used after a user has granted permission to your application, and you've received an authorization code. the returned access token allows your application to make api calls on behalf of the user. this endpoint should be called immediately after receiving the authorization code, as codes typically expire quickly. it's a crucial step in setting up api access for your slack integration.

**Action Parameters**

<ParamField path="client_id" type="string">
</ParamField>

<ParamField path="client_secret" type="string">
</ParamField>

<ParamField path="code" type="string">
</ParamField>

<ParamField path="redirect_uri" type="string">
</ParamField>

<ParamField path="single_channel" type="boolean">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_EXCHANGE_OAUTH_VERIFIER_FOR_ACCESS_TOKEN">
Exchanges a temporary oauth authorization code for an access token. this endpoint is a crucial part of the oauth 2.0 flow, allowing your application to obtain long-lived access tokens for making authenticated api calls to slack. it should be called immediately after receiving the temporary code from the user authorization step. the endpoint returns an access token along with other relevant information such as the authorized scopes and team details. this exchange should happen server-side to keep your client secret secure. note that the temporary code can only be used once and expires quickly, so make the exchange promptly after receiving it.

**Action Parameters**

<ParamField path="client_id" type="string">
</ParamField>

<ParamField path="client_secret" type="string">
</ParamField>

<ParamField path="code" type="string" required={true}>
</ParamField>

<ParamField path="redirect_uri" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_FETCH_BOT_USER_INFORMATION">
The 'bots.info' endpoint retrieves detailed information about a specific bot user in a slack workspace. it allows developers to fetch metadata and current status of a bot, which is useful for managing bot integrations or verifying bot properties. this endpoint should be used when you need to access up-to-date information about a bot, such as its name, icons, or other custom fields. it's particularly helpful for applications that manage multiple bots or need to display bot information to users. note that this endpoint only provides information about bots and not regular user accounts or other types of integrations.

**Action Parameters**

<ParamField path="bot" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_FETCH_CONVERSATION_HISTORY">
Retrieves the message history of a specified slack conversation or channel. this endpoint allows you to fetch a chronological list of messages and events that have occurred within a channel, group, or direct message conversation. it's particularly useful for applications that need to analyze conversation patterns, create chat archives, or display message history to users. the endpoint supports pagination for handling large message histories and allows filtering by time range. it returns detailed message objects including sender information, timestamps, and message content, making it a powerful tool for building slack integrations that require access to conversation data.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="inclusive" type="boolean">
</ParamField>

<ParamField path="latest" type="integer">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="oldest" type="integer">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_FETCH_CURRENT_TEAM_INFO_WITH_OPTIONAL_TEAM_SCOPE">
Retrieves detailed information about a slack team. this endpoint provides comprehensive data about the team's configuration, including its id, name, domain, email domain, and icon settings. if the team is part of an enterprise grid, it also returns the enterprise id and name. use this method when you need to access or verify team-specific details, such as during initial setup, user onboarding, or for administrative purposes. the endpoint is particularly useful for applications that interact with multiple slack teams and need to maintain accurate team information. note that while this method provides extensive team data, it does not include information about individual team members or channels.

**Action Parameters**

<ParamField path="team" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_FETCH_DND_STATUS_FOR_MULTIPLE_TEAM_MEMBERS">
Retrieves the do not disturb (dnd) status information for members of a slack team. this endpoint allows you to check the dnd settings for multiple users within a team simultaneously. it's particularly useful for team coordination, scheduling, or creating automation that respects users' dnd preferences. the endpoint returns current dnd statuses and any scheduled dnd sessions for the specified users or the entire team. note that this endpoint does not modify any dnd settings; it's read-only and meant for informational purposes.

**Action Parameters**

<ParamField path="users" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_FETCH_ITEM_REACTIONS">
The 'reactions.get' endpoint retrieves a list of all reactions for a specific item in slack, such as a message, file, or file comment. it allows developers to fetch detailed information about how users have reacted to content within slack. this endpoint is particularly useful for applications that need to analyze engagement, track popular content, or display reaction data outside of the slack interface. the method returns information about the reactions, including the types of reactions (emojis) used and, optionally, details about the users who reacted. while it provides comprehensive reaction data, it does not modify any reactions or return the content of the item itself.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="file" type="string">
</ParamField>

<ParamField path="file_comment" type="string">
</ParamField>

<ParamField path="full" type="boolean">
</ParamField>

<ParamField path="timestamp" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_FETCH_MESSAGE_THREAD_FROM_A_CONVERSATION">
The 'conversations.replies' endpoint retrieves a thread of messages within a slack conversation. it allows you to fetch replies to a specific message, providing a paginated list of messages in the thread. this endpoint is crucial for applications that need to display or process threaded conversations in slack channels, including public channels, private channels, and direct messages. use this when you need to access the full context of a discussion or when building features that involve message threads. note that the endpoint requires appropriate authentication scopes and may have rate limits for frequent requests.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="inclusive" type="boolean">
</ParamField>

<ParamField path="latest" type="integer">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="oldest" type="integer">
</ParamField>

<ParamField path="ts" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_FETCH_WORKSPACE_SETTINGS_INFORMATION">
Retrieves detailed information about the settings of a specific slack team (workspace) within an enterprise grid organization. this endpoint is crucial for administrators to view and manage workspace configurations across multiple teams. it provides comprehensive data about team settings, including but not limited to, the team's name, description, domain, icon, and other customizable options. use this endpoint when you need to audit, review, or prepare for modifications to a team's settings. note that this endpoint only fetches information and does not modify any settings; separate endpoints are required for making changes.

**Action Parameters**

<ParamField path="team_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_FIND_USER_BY_EMAIL_ADDRESS">
The users.lookupbyemail endpoint retrieves detailed information about a single slack user by searching for their registered email address. this method is particularly useful when you need to find a user's slack profile details but only have their email address available. the endpoint returns a comprehensive user object containing various fields such as user id, display name, and other profile information if the user is active. however, it will return a 'users not found' error if the user has been deactivated or if no user with the specified email exists in the workspace. this method requires the 'users:read.email' scope for authentication and should be used judiciously to respect user privacy.

**Action Parameters**

<ParamField path="email" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_GET_BILLABLE_TEAM_USER_INFO">
Retrieves billable information for members of a slack team or workspace. this endpoint provides details about which team members are considered billable users, which is crucial for understanding the billing structure and costs associated with your slack workspace. use this endpoint when you need to audit your team's billable members, prepare for billing cycles, or manage your workspace's user allocation. the endpoint returns information about all billable members, but it does not provide detailed billing amounts or invoke any changes to the billing status. it's important to note that this endpoint may have rate limits and should be used judiciously, typically for administrative or reporting purposes rather than real-time operations.

**Action Parameters**

<ParamField path="user" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_GET_CHANNEL_CONVERSATION_PREFERENCES">
The admin.conversations.getconversationprefs endpoint retrieves the current preferences and settings for a specific conversation (channel or direct message) in a slack workspace. it allows administrators to view important configuration details such as posting permissions, thread response settings, and potentially retention policies. this endpoint is crucial for auditing and managing conversation settings across an organization, especially in enterprise grid workspaces. it should be used when administrators need to review or verify the current configuration of a conversation before making any changes. note that this endpoint likely requires admin-level permissions and appropriate oauth scopes to access.

**Action Parameters**

<ParamField path="channel_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_GET_CHANNEL_S_CONNECTED_WORKSPACES_IN_ENTERPRISE">
Retrieves the list of teams associated with a specific slack conversation. this endpoint is designed for slack workspace administrators or organization owners to manage multi-workspace channels. it allows admins to identify which teams (workspaces) have access to a particular conversation, which is crucial for maintaining proper access control and understanding the scope of shared channels. the endpoint should be used when auditing channel access, managing cross-workspace collaborations, or troubleshooting team-related issues in shared channels. note that this endpoint only provides team information and does not allow for modifying team associations.

**Action Parameters**

<ParamField path="channel_id" type="string" required={true}>
</ParamField>

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_GET_CURRENT_TEAM_INTEGRATION_LOGS">
Retrieves the integration activity logs for a specified slack team. this endpoint provides detailed information about changes to integrations within the team, including when integrations are added, removed, enabled, disabled, or updated. it's particularly useful for team administrators and security personnel who need to monitor and audit integration-related activities. the logs include information such as the type of change, the service affected, the user who made the change, and the timestamp of the activity. this tool should be used when there's a need to track integration changes, investigate security concerns, or maintain compliance records related to third-party app usage within a slack workspace. it does not provide real-time monitoring and may have some delay in reflecting the most recent activities.

**Action Parameters**

<ParamField path="app_id" type="string">
</ParamField>

<ParamField path="change_type" type="string">
</ParamField>

<ParamField path="count" type="string">
</ParamField>

<ParamField path="page" type="string">
</ParamField>

<ParamField path="service_id" type="string">
</ParamField>

<ParamField path="user" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_GET_CURRENT_TEAM_S_ACCESS_LOGS">
Retrieves access logs for users on a slack workspace, providing detailed information about user logins and activity. this endpoint is crucial for security monitoring, compliance auditing, and tracking user access patterns. it returns a chronological list of access events, including user identifiers, ip addresses, and geographical data. use this endpoint when you need to investigate suspicious activity, perform regular security audits, or generate reports on workspace access. note that this endpoint may have rate limits and is typically restricted to workspace administrators or security personnel with appropriate permissions.

**Action Parameters**

<ParamField path="before" type="string">
</ParamField>

<ParamField path="count" type="string">
</ParamField>

<ParamField path="page" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_GET_REMINDER_INFORMATION">
Retrieves detailed information about a specific reminder in slack. this endpoint allows you to fetch the complete details of a reminder, including its content, creator, time, and status. use this method when you need to access or verify the information of an existing reminder. it's particularly useful for applications that manage or display reminders, allowing them to show up-to-date information about a specific reminder. the endpoint returns a comprehensive set of data about the reminder but does not modify or interact with the reminder in any way. note that this method only retrieves information for reminders that are accessible to the authenticated user in their slack workspace.

**Action Parameters**

<ParamField path="reminder" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_GET_USER_PRESENCE_INFO">
Retrieves the current presence status of a specified slack user. this endpoint allows you to check whether a user is currently active or away on the slack platform. it's useful for applications that need to track user availability or trigger actions based on a user's presence. the endpoint returns the user's current status (active or away), whether they're online, and potentially the timestamp of their last activity. it should be used when you need real-time information about a user's availability in slack. note that this endpoint doesn't provide detailed information about the user's activities or the reason for their current status.

**Action Parameters**

<ParamField path="user" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_INDICATE_WORKFLOW_STEP_FAILURE">
Retrieves information about a failed step in a slack workflow. this endpoint is used to get details on why a specific step in a workflow execution failed, providing crucial information for debugging and error resolution. it should be called when a workflow step fails and you need to understand the cause of the failure. the endpoint returns data about the failed step, which may include an error message, the step id, and potentially the workflow context at the time of failure. this tool is essential for maintaining and troubleshooting automated processes within slack, ensuring smooth operation of custom workflows. note that this endpoint does not retry or fix the failed step; it only provides information about the failure.

**Action Parameters**

<ParamField path="error" type="string" required={true}>
</ParamField>

<ParamField path="workflow_step_execute_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_INITIATES_CHANNEL_BASED_CONVERSATIONS">
Creates a new conversation (channel) in slack, either public or private. this endpoint should be used when you need to programmatically create channels for team communication, project management, or automated workflows. it allows for the creation of organized spaces within a slack workspace for specific topics, teams, or projects. the created channel will be empty initially, and members will need to be invited separately. note that channel names must be unique within a workspace, and there may be limitations on the number of channels that can be created based on the workspace's plan.

**Action Parameters**

<ParamField path="is_private" type="boolean">
</ParamField>

<ParamField path="name" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_INVALIDATE_A_USER_SESSION_BY_ID">
The admin.users.session.invalidate endpoint allows slack workspace administrators to forcibly terminate a specific user's active session. this tool is primarily used for security purposes, such as when an admin needs to immediately revoke access for a compromised account or ensure a user is logged out across all devices. it requires both the team id and the specific session id to target the correct session for invalidation. this endpoint should be used cautiously and only when necessary, as it will disrupt the user's current slack session, forcing them to log in again. it's particularly useful in scenarios where simply changing a user's password may not be sufficient to ensure immediate session termination.

**Action Parameters**

<ParamField path="session_id" type="integer" required={true}>
</ParamField>

<ParamField path="team_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_INVITE_USERS_TO_A_SLACK_CHANNEL">
The conversations.invite endpoint allows you to invite one or multiple users to a specific slack channel. this powerful tool streamlines the process of adding members to your conversations, enhancing team collaboration. use this endpoint when you need to quickly add users to a channel, especially for onboarding new team members or creating project-specific groups. it supports both public and private channels, providing flexibility in managing your slack workspace. the endpoint can handle bulk invitations of up to 1000 users in a single request, making it efficient for large-scale channel population. however, be mindful of your slack plan's limitations and ensure all user ids are valid to avoid errors.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="users" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_INVITE_USER_TO_CHANNEL">
Invites specified users to a slack channel. this endpoint allows administrators to add multiple users to a public or private channel simultaneously. it's particularly useful for onboarding new team members or reorganizing channel memberships. the method can handle up to 1000 user invitations at once, making it efficient for bulk operations. however, it's important to note that this endpoint is only available for enterprise grid workspaces, indicating its use is restricted to more advanced slack environments. the calling user must have appropriate permissions and be a member of the target channel.

**Action Parameters**

<ParamField path="channel_id" type="string" required={true}>
</ParamField>

<ParamField path="user_ids" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_INVITE_USER_TO_WORKSPACE_WITH_OPTIONAL_CHANNEL_INVITES">
The admin.users.invite endpoint allows slack workspace administrators to invite new users to their workspace. this powerful tool streamlines the process of adding members, setting up guest accounts, and managing initial channel access. use this endpoint when you need to programmatically invite users, especially in bulk or as part of an automated onboarding process. it's particularly useful for large organizations or when integrating slack with other systems. the endpoint provides flexibility in setting user roles, specifying channel access, and managing guest account expirations. however, it should be used cautiously, as it directly affects workspace membership and access rights. note that while you can set initial parameters, users may still need to complete the signup process and might have the ability to modify some settings upon joining.

**Action Parameters**

<ParamField path="channel_ids" type="string" required={true}>
</ParamField>

<ParamField path="custom_message" type="string">
</ParamField>

<ParamField path="email" type="string" required={true}>
</ParamField>

<ParamField path="guest_expiration_ts" type="string">
</ParamField>

<ParamField path="is_restricted" type="boolean">
</ParamField>

<ParamField path="is_ultra_restricted" type="boolean">
</ParamField>

<ParamField path="real_name" type="string">
</ParamField>

<ParamField path="resend" type="boolean">
</ParamField>

<ParamField path="team_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_JOIN_AN_EXISTING_CONVERSATION">
Joins the authenticated user to a slack conversation (channel). this endpoint allows you to add yourself to an existing public or private channel, or a multi-person direct message. use this when you need to participate in a conversation that you're not currently a member of. it's particularly useful for bots or integrations that need to join specific channels to listen for messages or post updates. note that this endpoint doesn't create new conversations; it only allows joining existing ones. also, be aware that joining private channels may require additional permissions or invitations, depending on your workspace settings and the user's role.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LEAVE_A_CONVERSATION">
The 'conversations.leave' endpoint allows a user to exit a slack conversation, which can be a public channel, private channel, direct message, or group direct message. this method is useful when a user no longer wishes to participate in a specific conversation or needs to remove themselves from a channel. it should be used cautiously, as leaving a conversation may result in missing important team communications. the endpoint will fail if the user is not currently a member of the specified conversation or if they are the last member of a private channel (which would effectively archive the channel). note that this method cannot be used to leave shared channels across organizations.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LISTS_PINNED_ITEMS_IN_A_CHANNEL">
The 'pins.list' endpoint retrieves a list of items that have been pinned to a specified slack channel. it allows developers to access important or frequently referenced messages or files that team members have marked as significant. this endpoint is particularly useful for creating custom pin management tools, archiving important information, or integrating pinned content into other applications. the endpoint supports pagination for efficiently handling channels with a large number of pinned items.

**Action Parameters**

<ParamField path="channel" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="array">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LISTS_USER_S_STARRED_ITEMS_WITH_PAGINATION">
Retrieves a list of all starred items for the authenticated user in a slack workspace. this endpoint allows you to fetch items that a user has marked as favorites, including messages, files, and channels. it's useful for applications that need to display or manage a user's starred content within slack. the endpoint returns a comprehensive list of starred items without any filtering options, so it's best used when you need a complete overview of a user's favorites. note that the response may include various types of items, so your application should be prepared to handle different data structures for messages, files, and channels.

**Action Parameters**

<ParamField path="count" type="string">
</ParamField>

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="page" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_ACCESSIBLE_CONVERSATIONS_FOR_A_USER">
Retrieves a list of conversations (channels, direct messages, and group messages) that a specified user or the authenticated user is a member of in a slack workspace. this endpoint is useful for applications that need to analyze user participation, manage channel memberships, or provide custom navigation within a slack integration. it supports pagination for handling large numbers of conversations and allows filtering by conversation types. use this endpoint when you need to gather information about a user's conversation memberships or when building user-specific views of slack workspaces. note that the results are limited to conversations the authenticated token has access to view.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="exclude_archived" type="boolean">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="types" type="string">
</ParamField>

<ParamField path="user" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_ALL_SLACK_TEAM_CHANNELS_WITH_VARIOUS_FILTERS">
Retrieves a list of all conversations in a slack workspace, including public channels, private channels, multi-person direct messages (mpim), and direct messages (im). this endpoint is essential for obtaining an overview of all available conversations, which can be used for workspace analysis, channel management, or as a precursor to performing actions on specific conversations. the method supports filtering by conversation types, exclusion of archived channels, and pagination for handling large workspaces efficiently. use this endpoint when you need to display available channels to users, perform workspace-wide operations, or gather data about the structure of a slack workspace. note that the returned list may be affected by the permissions of the authenticated user, and some conversations might be excluded if the user doesn't have access to them.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="exclude_archived" type="boolean">
</ParamField>

<ParamField path="limit" type="integer" default="1">
</ParamField>

<ParamField path="types" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_ALL_SLACK_TEAM_USERS_WITH_PAGINATION">
The 'users.list' endpoint retrieves a comprehensive list of users within a slack workspace. it provides detailed information about each user, including their profile data, account settings, and team memberships. this endpoint is particularly useful for applications that need to synchronize user data, manage user permissions, or perform bulk operations on user accounts. the response includes both active and deactivated users, allowing for a complete overview of the workspace's user base. however, it's important to note that the endpoint may not return real-time data, and there might be a slight delay in reflecting recent changes to user accounts.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="include_locale" type="boolean">
</ParamField>

<ParamField path="limit" type="integer" default="1">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_ALL_TEAMS_IN_AN_ENTERPRISE_ORGANIZATION">
Retrieves a list of all teams within a slack enterprise grid organization. this endpoint is part of the admin api and is designed for administrators to get an overview of the teams in their organization. it should be used when you need to audit, manage, or report on the teams across your entire slack instance. the endpoint returns basic information about each team, which may include team ids, names, and other relevant metadata. note that this endpoint requires admin-level permissions and is only available for enterprise grid organizations. it does not provide detailed information about team members or channel structures; for such data, you would need to use additional api calls.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_ALL_USERS_IN_A_USER_GROUP">
Retrieves a list of users belonging to a specified usergroup in slack. this endpoint is particularly useful for administrators and developers who need to manage usergroup memberships, audit access control, or synchronize user data with external systems. it provides a comprehensive view of the usergroup's composition, including options to include disabled users and paginate through large usergroups. the endpoint should be used when you need to obtain detailed information about usergroup membership, but it does not provide capabilities to modify the usergroup or user attributes directly.

**Action Parameters**

<ParamField path="include_disabled" type="boolean">
</ParamField>

<ParamField path="usergroup" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_APPROVED_APPS_FOR_ORG_OR_WORKSPACE">
Retrieves a list of approved apps for a slack workspace or enterprise grid organization. this endpoint is designed for administrators to view and manage the apps that have been approved for use within their slack environment. it can be used to audit the current set of approved apps, verify recent approvals, or gather information for reporting purposes. the endpoint supports pagination for handling large lists of approved apps and can be scoped to a specific workspace within an enterprise grid organization if needed.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="enterprise_id" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_APPROVED_WORKSPACE_INVITE_REQUESTS">
Retrieves a list of approved invite requests for a slack workspace. this endpoint allows administrators to view and manage successful invitations, providing insights into workspace growth and user onboarding. it's particularly useful for large organizations or those using slack enterprise grid to monitor approved invitations across multiple workspaces. the endpoint supports pagination for efficient handling of large datasets and can be used to audit the invitation process or gather statistics on workspace expansion.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_APP_PERMISSIONS_ON_A_TEAM">
Retrieves detailed information about the permissions granted to a slack application. this endpoint is used to audit and review the current set of permissions associated with an app, helping developers and administrators understand the app's capabilities within a slack workspace. it provides a comprehensive list of all permissions the app has been granted, which is crucial for security audits, troubleshooting, or when preparing to request additional scopes. the endpoint does not modify any permissions; it only returns the current state. note that the permissions returned are specific to the app associated with the authentication token used in the request.

**Action Parameters**

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_APP_PERMISSIONS_SCOPES_ON_A_TEAM">
Retrieves a list of oauth scopes associated with the authenticated slack app. this endpoint allows developers to view the current permissions granted to their app within a slack workspace. it's particularly useful for auditing app permissions, verifying the app's access levels, or preparing for permission changes. the endpoint returns all scopes the app has been granted, which can include both bot and user token scopes. it should be used when you need to review or confirm the current set of permissions for your slack app. note that this endpoint does not modify any permissions; it only provides a read-only view of the existing scopes.

**Action Parameters**

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_APP_RESOURCE_GRANTS_ON_A_TEAM">
Retrieves a list of resources that the authenticated slack app has permission to access within the workspace. this endpoint is used to get an overview of the app's current access levels and the specific slack resources it can interact with, such as channels, users, or messages. it's particularly useful for app developers to audit their app's permissions or to dynamically adjust the app's behavior based on its current access rights. the endpoint does not modify any permissions; it only provides a read-only view of the current state. note that the response will be limited to the resources accessible under the current authentication scope, so it may not represent all possible resources in a slack workspace.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_APP_USER_GRANTS_AND_SCOPES_ON_TEAM">
Retrieves a list of user permissions within a slack workspace. this endpoint allows administrators or authorized apps to fetch the permissions assigned to users, providing insight into access levels and capabilities of individual users within the slack environment. it's particularly useful for auditing user access, managing permissions at scale, or integrating with external user management systems. the endpoint returns comprehensive information about user permissions, which may include details such as oauth scopes, custom function access, and any specific role-based permissions. note that this endpoint does not modify permissions; it's purely for information retrieval purposes.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_DENIED_WORKSPACE_INVITE_REQUESTS">
Retrieves a list of denied workspace invite requests for a slack workspace. this endpoint is used to review and manage invite requests that have been previously denied. it's particularly useful for administrators who need to audit access control or reconsider denied requests. the endpoint supports pagination to handle large numbers of denied requests efficiently. it should be used when there's a need to review the history of denied invitations, potentially for security audits or to reconsider specific denials. note that this endpoint only provides information about denied requests and does not include details about approved or pending invitations.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_DISCONNECTED_CHANNELS_WITH_ORIGINAL_IDS_FOR_EKM">
Retrieves a list of original connected channel information for enterprise key management (ekm) enabled channels in a slack workspace. this endpoint is specifically designed for administrators managing ekm in slack, allowing them to obtain crucial information about encrypted channels, particularly those that have been frozen. it's essential for maintaining security and compliance in organizations using ekm to protect sensitive data in slack. the endpoint should be used when auditing ekm-enabled channels or preparing to revoke encryption keys for frozen channels. it does not modify any data and is intended for informational purposes only. note that this endpoint requires admin-level permissions and is only applicable to workspaces with ekm enabled.

**Action Parameters**

<ParamField path="channel_ids" type="string">
</ParamField>

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="team_ids" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_ENTERPRISE_GRID_ORGANIZATION_EMOJIS">
Retrieves a list of all custom emojis within a slack enterprise grid organization. this endpoint is specifically designed for administrators to manage and oversee custom emoji usage across the entire organization. it provides a comprehensive view of all user-created emojis, which can be useful for monitoring appropriate usage, identifying popular custom emojis, or managing emoji-related policies. the endpoint does not list standard slack emojis, focusing solely on custom additions. use this when you need to audit, report on, or manage custom emojis at an organizational level. note that this endpoint requires admin-level permissions and is part of the slack admin api, making it suitable for administrative tools and dashboards rather than end-user applications.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_EVENT_AUTHORIZATIONS_FOR_APPS">
Retrieves a list of authorizations for a specific event context in slack. this endpoint allows developers to determine which app installations are visible and have the necessary permissions to access or respond to a particular event. it's essential for understanding the scope of an app's visibility and capabilities across different slack workspaces and events. the endpoint supports pagination for handling large sets of authorizations, making it suitable for apps installed in numerous workspaces or with complex permission structures.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="event_context" type="string" required={true}>
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_FILES_WITH_FILTERS_IN_SLACK">
The 'files.list' endpoint allows you to retrieve a list of files within a slack workspace. it provides a comprehensive view of files shared across channels, enabling efficient file management and content discovery. use this endpoint when you need to fetch file metadata, such as names, types, and sharing information, without downloading the actual file contents. the endpoint supports various filtering options, allowing you to narrow down results by channel, time range, or file type. it's particularly useful for applications that need to audit file usage, create file indexes, or build custom file management interfaces within slack. note that this endpoint returns metadata only and does not provide direct file downloads; for that, you would need to use a separate file retrieval endpoint.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="count" type="string">
</ParamField>

<ParamField path="page" type="string">
</ParamField>

<ParamField path="show_files_hidden_by_limit" type="boolean">
</ParamField>

<ParamField path="ts_from" type="integer">
</ParamField>

<ParamField path="ts_to" type="integer">
</ParamField>

<ParamField path="types" type="string">
</ParamField>

<ParamField path="user" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_IDP_GROUPS_LINKED_TO_A_CHANNEL">
Retrieves a list of identity provider (idp) groups that have been granted access to a specific private channel or conversation in a slack workspace. this endpoint is designed for slack workspace administrators to manage and review access control settings for conversations. it should be used when auditing or modifying access restrictions on private channels. the tool provides visibility into which external groups (managed by an identity provider) have permission to access certain conversations, enhancing security and compliance management. note that this endpoint does not modify any permissions; it only lists the current access configuration.

**Action Parameters**

<ParamField path="channel_id" type="string" required={true}>
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_ORG_LEVEL_IDP_GROUP_CHANNELS">
Retrieves a list of channels associated with a specific user group in a slack workspace. this endpoint is particularly useful for administrators who need to audit or manage channel access for user groups. it provides visibility into which channels a user group has been added to, helping in access control and organization of the slack workspace. the endpoint should be used when you need to review or verify channel associations for a particular user group, but it will not modify any existing settings or permissions.

**Action Parameters**

<ParamField path="include_num_members" type="boolean">
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

<ParamField path="usergroup_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_PENDING_WORKSPACE_INVITE_REQUESTS">
The admin.inviterequests.list endpoint retrieves a list of pending or active invite requests for a specified slack workspace. this tool is designed for workspace administrators to review and manage incoming invitation requests, providing crucial control over workspace access and growth. it supports pagination for efficient handling of large datasets, allowing administrators to iterate through all invite requests in manageable chunks. the endpoint is particularly useful for monitoring workspace expansion, ensuring compliance with company policies, and maintaining security by overseeing who is being invited to join the workspace.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_RESTRICTED_APPS_FOR_AN_ORG_OR_WORKSPACE">
Retrieves a list of all apps that are restricted from installation on a slack workspace or across an entire enterprise grid organization. this endpoint is essential for enterprise grid administrators to manage and review app restrictions, ensuring compliance with organizational policies. it provides detailed information about each restricted app, including its id, name, and developer type (internal, third-party, or slack-built). use this endpoint when you need to audit your organization's app restrictions or verify the current status of app installation policies. note that this method only works for enterprise grid workspaces and requires appropriate admin-level permissions to access.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="enterprise_id" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_SCHEDULED_MESSAGES_IN_A_CHANNEL">
The chat.scheduledmessages.list endpoint retrieves a list of scheduled messages for a slack workspace or specific channel. it allows users to view pending messages that have been scheduled for future delivery, providing details such as the message content, scheduled time, and target channel. this endpoint is particularly useful for managing and reviewing upcoming automated communications or delayed announcements within a slack environment. the endpoint supports pagination and filtering options, enabling efficient retrieval of large sets of scheduled messages or focusing on specific time ranges and channels. it's important to note that this endpoint only returns messages that have not yet been sent and are still pending delivery. developers should use this endpoint when building applications that need to monitor, manage, or display scheduled message queues, such as dashboard tools for content calendars or automation systems that need to track pending notifications.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="latest" type="integer">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="oldest" type="integer">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_SLACK_S_REMOTE_FILES_WITH_FILTERS">
Retrieves a list of remote files that have been added to your slack workspace. this endpoint is useful for tracking and managing external files that have been shared or referenced within slack conversations. it allows you to fetch metadata about these remote files, such as their names, types, and sharing information. use this endpoint when you need to audit remote file usage, compile reports on external content, or build integrations that interact with shared remote files. the endpoint supports pagination for handling large sets of results and offers filtering options based on timestamps and channel-specific queries. note that this endpoint only returns metadata about the remote files and does not provide access to the actual file contents.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="ts_from" type="integer">
</ParamField>

<ParamField path="ts_to" type="integer">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_TEAM_CUSTOM_EMOJIS">
The emoji.list endpoint retrieves a comprehensive list of custom emojis available in the slack workspace. this get method allows developers to access the names and corresponding image urls of all custom emojis that have been added to the workspace. it's particularly useful for applications or bots that need to display, use, or manage custom emojis within slack integrations. the endpoint returns both standard slack emojis and any custom emojis uploaded by workspace members. however, it does not provide usage statistics or creation dates for the emojis. use this endpoint when you need to synchronize emoji data with an external system or when building features that involve emoji selection or display within your slack integration.

**Action Parameters**

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_TEAM_WORKSPACE_APP_REQUESTS">
Retrieves a list of app installation requests for a slack workspace or enterprise grid organization. this endpoint allows administrators to view pending requests from users who want to install new slack apps. it's essential for maintaining control over the apps used within your slack environment and ensuring compliance with organizational policies. the endpoint supports pagination for handling large numbers of requests efficiently. use this when you need to review, approve, or deny app installation requests across your slack workspace or organization.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_USER_GROUPS_FOR_TEAM_WITH_OPTIONS">
Retrieves a list of all user groups in a slack workspace. this endpoint is used to get an overview of the organizational structure within a slack team, including details about each user group such as their names, handles, descriptions, and member counts. it's particularly useful for administrators or integrations that need to manage or audit user groups across the workspace. the endpoint returns information about both user-created groups and default groups provided by slack. note that while this endpoint doesn't require any parameters, the response may be paginated for workspaces with a large number of user groups, and additional api calls might be necessary to retrieve the complete list.

**Action Parameters**

<ParamField path="include_count" type="boolean">
</ParamField>

<ParamField path="include_disabled" type="boolean">
</ParamField>

<ParamField path="include_users" type="boolean">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_USER_REACTIONS">
The reactions.list endpoint retrieves a list of all reactions for items within a specified slack channel or conversation. it allows you to fetch reaction data, which can include emojis or custom reactions added to messages or files. this endpoint is particularly useful for analyzing user engagement, tracking popular content, or building analytics tools that monitor interaction patterns within slack workspaces. the method supports pagination for handling large sets of reaction data and can provide detailed information about each reaction when requested. it's important to note that this endpoint focuses solely on reaction data and does not return the content of messages or files themselves.

**Action Parameters**

<ParamField path="count" type="integer">
</ParamField>

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="full" type="boolean">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="page" type="integer">
</ParamField>

<ParamField path="user" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_USER_REMINDERS_WITH_DETAILS">
Retrieves a list of all reminders for the authenticated user in their slack workspace. this endpoint is useful for applications that need to manage or display upcoming reminders, such as task management tools or personal assistants. it provides an overview of all scheduled reminders, including their details like content, due date, and status. the endpoint should be used when you need to fetch all reminders at once, rather than querying for individual reminders. note that this endpoint may have rate limits and pagination for workspaces with a large number of reminders, although these details are not specified in the given schema.

**Action Parameters**

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_WORKSPACE_ADMINS">
Retrieves a list of administrators for a specified slack team (workspace) within an enterprise grid organization. this endpoint is designed for org owners and admins to manage and audit administrative access across their enterprise grid workspaces. it provides information about users who have administrative privileges for a given team, helping in access control and compliance monitoring. the endpoint supports pagination for efficient handling of large admin lists. use this when you need to review or manage the administrative structure of a specific workspace in your enterprise grid.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="team_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_WORKSPACE_OWNERS">
Retrieves a list of owners for a specified slack workspace within an enterprise grid organization. this endpoint allows administrators to view all users with owner privileges in a particular team. it's useful for auditing ownership, managing permissions, or when transitioning team responsibilities. the endpoint supports pagination for efficiently handling workspaces with a large number of owners. note that this endpoint is exclusive to enterprise grid customers and cannot be used with standard slack workspaces.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="team_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_LIST_WORKSPACE_USERS">
The admin.users.list method retrieves a paginated list of users with administrative privileges in a slack workspace. it allows workspace owners and admins to view and manage the administrative user base efficiently. this endpoint is particularly useful for auditing admin access, reviewing admin assignments, or gathering data for reporting purposes. the method returns basic information about each admin user, such as their user id, name, and email. it does not provide detailed information about user permissions or roles beyond their admin status.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="team_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_MANUALLY_SET_USER_PRESENCE">
Manually sets a user's presence status in slack. this endpoint allows you to override the automatic presence detection and explicitly set a user's status to either 'auto' (letting slack determine presence based on activity) or 'away'. use this when you need to programmatically update a user's availability, such as when integrating with external systems or custom workflows. note that setting presence to 'auto' doesn't guarantee an 'active' status; it simply reverts to slack's default presence behavior. this change persists between connections but can be overridden by user actions or slack's auto-away feature after 10 minutes of inactivity.

**Action Parameters**

<ParamField path="presence" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_MAP_USER_IDS_FOR_ENTERPRISE_GRID_WORKSPACES">
The migration.exchange endpoint facilitates the conversion of local slack user ids to global user ids for enterprise grid organizations. this tool is essential during the migration process when transitioning from a standard slack workspace to an enterprise grid deployment. it allows administrators and developers to maintain user identity consistency across the organization by mapping local, workspace-specific user ids to universal, enterprise grid-compatible global user ids. the endpoint should be used when preparing for or executing a migration to enterprise grid, or when integrating systems that need to work with both local and global user ids. it's important to note that this endpoint does not perform the actual migration but provides the necessary id mapping for the process.

**Action Parameters**

<ParamField path="team_id" type="string">
</ParamField>

<ParamField path="to_old" type="boolean">
</ParamField>

<ParamField path="users" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_MARK_A_USER_AS_ACTIVE_DEPRECATED">
The users.setactive endpoint marks a user as active in a slack workspace. this method is used to update the user's status, indicating that they are currently available and engaged with slack. it's particularly useful for automatically updating a user's status when they return from being away or inactive. the endpoint doesn't require any specific parameters in the request body. however, it's important to note that this method should be called with appropriate authentication, typically using a user token or bot token with necessary permissions. while it sets a user's status to active, it doesn't provide any additional information about the user or their previous status.

**Action Parameters**

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_MARK_REMINDER_AS_COMPLETE">
The 'reminders.complete' endpoint marks a specific slack reminder as complete. it should be used when a user wants to indicate that a task associated with a reminder has been finished. this endpoint is particularly useful for integrations that manage tasks or to-do lists within slack. however, it's important to note that this endpoint has been deprecated since march 2023, which means it may not be reliable for new applications and could stop working in the future. as an alternative, consider using other reminder management methods or consult slack's latest documentation for recommended approaches to handle reminders.

**Action Parameters**

<ParamField path="reminder" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_MARK_WORKFLOW_STEP_AS_COMPLETED">
The 'workflows.stepcompleted' endpoint is used to mark a specific step within a slack workflow as completed. this get request is crucial for tracking the progress of automated workflows and potentially triggering subsequent actions. it should be called when a custom step in a workflow has finished its execution successfully. this endpoint helps in maintaining the state of complex workflows, especially those involving custom functions or integrations with external services. it's important to note that this endpoint doesn't execute the step itself but rather updates its status to completed. use this endpoint in conjunction with workflow creation and management to build sophisticated automation sequences within slack.

**Action Parameters**

<ParamField path="outputs" type="string">
</ParamField>

<ParamField path="workflow_step_execute_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_OPEN_A_VIEW_FOR_A_SLACK_USER">
The views.open endpoint allows you to open a modal view in a slack workspace. this method is used to create interactive, customized interfaces for users within slack, such as forms, detailed information displays, or multi-step workflows. it's particularly useful when you need to collect information from users or display complex data in a structured format. the view appears as a pop-up window, providing a focused environment for user interaction. keep in mind that views have a limited lifespan and should be used for immediate user interactions rather than long-term display of information.

**Action Parameters**

<ParamField path="trigger_id" type="string" required={true}>
</ParamField>

<ParamField path="view" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_OPEN_OR_RESUME_DIRECT_OR_MULTI_PERSON_MESSAGES">
The conversations.open endpoint opens or resumes a direct message (dm) or multi-person direct message (mpim) conversation in slack. it allows users to start new conversations or continue existing ones by providing either a conversation id or a list of user ids. this method is essential for initiating private communications between two or more users within a slack workspace. it's particularly useful for integrations that need to create or access private conversations programmatically. the endpoint supports both 1:1 and group conversations, making it versatile for various communication needs. when using this endpoint, it's important to note that you must provide either a 'channel' id for existing conversations or a list of 'users' for new or existing conversations, but not both simultaneously.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="return_im" type="boolean">
</ParamField>

<ParamField path="users" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_OPEN_USER_DIALOG_WITH_JSON_DEFINED_UI">
Opens an interactive dialog in slack, allowing your app to collect information from users through a modal interface. this method is essential for creating custom forms, gathering user input, or displaying complex information within the slack workspace. use this endpoint when you need to prompt users for multiple pieces of information or when you want to present a structured interface for data entry. the dialog appears as a modal window, providing a focused interaction experience. note that dialogs can only be opened in response to a user action, such as clicking a button, and require a valid trigger id. the dialog's appearance and behavior are highly customizable, but they must adhere to slack's design guidelines and size limitations.

**Action Parameters**

<ParamField path="dialog" type="string" required={true}>
</ParamField>

<ParamField path="trigger_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_PINS_AN_ITEM_TO_A_CHANNEL">
The 'pins.add' endpoint allows you to pin a specific message within a slack channel. this function is used to highlight important information, making it easily accessible to channel members. when invoked, it adds a pin to the specified message, causing it to appear in the channel's pinned items section. this is particularly useful for marking crucial announcements, decisions, or reference information that team members need to access frequently. the endpoint requires specifying the target channel and ideally the exact timestamp of the message to be pinned. it's important to note that this action may be subject to user permissions within the slack workspace.

**Action Parameters**

<ParamField path="channel" type="string" required={true}>
</ParamField>

<ParamField path="timestamp" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_PROMOTE_USER_TO_ADMIN_STATUS">
This endpoint allows you to designate a user as an admin within a specific slack workspace. it is used when you need to promote a regular user to an admin role, granting them additional permissions and control over workspace settings. the operation requires both the workspace id and the user id of the person being promoted. this action should be used carefully, as admins have significant control over workspace configurations and user management. note that this endpoint only sets the admin status and does not provide any feedback on the success of the operation or the current status of the user.

**Action Parameters**

<ParamField path="team_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_PROMOTE_USER_TO_WORKSPACE_OWNER">
The admin.users.setowner endpoint promotes an existing user to the role of workspace owner within a slack enterprise grid organization. this powerful administrative function should be used when you need to grant full ownership privileges to a user, typically for management restructuring or when the current owner is leaving the organization. it's crucial to use this endpoint carefully, as workspace owners have extensive control over the slack environment. this method only works for enterprise grid workspaces and requires appropriate administrative permissions. note that this action cannot be undone through the api, and changing ownership may have significant implications for workspace management and security.

**Action Parameters**

<ParamField path="team_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_PUBLISH_STATIC_VIEW_FOR_USER">
The views.publish endpoint is used to publish or update a home tab view for a specific user in a slack workspace. this method allows you to create or modify the custom interface that appears in the home tab of the slack client for a particular user. it's primarily used for displaying personalized, dynamic content or interactive elements specific to your slack app. the endpoint should be used when you want to update a user's home tab with new information, metrics, or interactive components. it's particularly useful for creating dashboard-like interfaces or providing user-specific controls and information. note that this endpoint specifically targets the home tab view and should not be used for creating or updating modals or messages in channels.

**Action Parameters**

<ParamField path="hash" type="string">
</ParamField>

<ParamField path="user_id" type="string" required={true}>
</ParamField>

<ParamField path="view" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_PUSH_VIEW_TO_ROOT_VIEW_STACK">
The views.push endpoint adds a new view to the top of the existing view stack in a slack modal. it's used for creating dynamic, multi-step interactions within a single modal session. this method allows apps to respond to user actions by presenting new content or forms without closing the current modal. it's particularly useful for workflows that require multiple stages or for displaying additional information based on user input. the endpoint requires a valid trigger id from a recent interaction and must be called within 3 seconds of that interaction. it's important to note that there's a limit to the number of views that can be pushed onto a single modal stack, typically around 3 views.

**Action Parameters**

<ParamField path="trigger_id" type="string" required={true}>
</ParamField>

<ParamField path="view" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REGISTERS_A_NEW_CALL_WITH_PARTICIPANTS">
The 'calls.add' endpoint registers a new call in slack, integrating third-party call services with slack's interface. it allows you to create a call with specific details such as title, start time, and participants, providing urls for joining the call through slack or directly through desktop applications. this endpoint is particularly useful for setting up team meetings, conference calls, or any voice/video communication within slack channels. it supports both immediate and scheduled calls, offering flexibility in call management and participant organization. use this when you need to programmatically create and manage calls within your slack workspace, especially when integrating external calling services.

**Action Parameters**

<ParamField path="created_by" type="string">
</ParamField>

<ParamField path="date_start" type="integer">
</ParamField>

<ParamField path="desktop_app_join_url" type="string">
</ParamField>

<ParamField path="external_display_id" type="string">
</ParamField>

<ParamField path="external_unique_id" type="string" required={true}>
</ParamField>

<ParamField path="join_url" type="string" required={true}>
</ParamField>

<ParamField path="title" type="string">
</ParamField>

<ParamField path="users" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REGISTERS_NEW_CALL_PARTICIPANTS">
Adds one or more participants to an ongoing slack call. this endpoint should be used when you need to include additional users in an existing call, such as when inviting late joiners or expanding the call's participants. it requires the unique identifier of the call and a list of user ids to be added. the endpoint is particularly useful for managing dynamic call participation in collaborative environments. note that this method can only add participants to calls that have already been initiated using the 'calls.add' method.

**Action Parameters**

<ParamField path="id" type="string" required={true}>
</ParamField>

<ParamField path="users" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REGISTER_CALL_PARTICIPANTS_REMOVAL">
This endpoint removes specified participants from an ongoing slack call. it should be used when you need to programmatically remove one or more users from a call, such as when a participant needs to be ejected or when automating call management. the endpoint requires the call id and a list of users to remove. it's important to note that this action is irreversible, and removed users will need to be re-added if they should rejoin the call. this method will not end the call even if all participants are removed. be cautious when using this endpoint, as it directly affects user participation in ongoing communications.

**Action Parameters**

<ParamField path="id" type="string" required={true}>
</ParamField>

<ParamField path="users" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REMINDERS_ADD">
The 'reminders.add' endpoint allows you to create new reminders in slack. it's designed to help users set notifications for future tasks, events, or any information they need to remember. this tool is particularly useful for managing personal tasks, setting team reminders, or scheduling follow-ups within slack's collaborative environment. the endpoint offers flexibility in timing, supporting one-time reminders as well as recurring ones through natural language input. while it focuses on creating reminders, it does not provide functionality for listing, modifying, or deleting existing reminders. note that if no specific user is designated, the reminder defaults to the creator, making it versatile for both personal and team use.(DEPRECATED use create_a_reminder)

**Action Parameters**

<ParamField path="text" type="string" required={true}>
</ParamField>

<ParamField path="time" type="string" required={true}>
</ParamField>

<ParamField path="user" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REMOVE_A_REMOTE_FILE">
Removes a remote file from slack. this endpoint is used to delete the reference to an external file that was previously added to slack using the remote files api. it's particularly useful for maintaining consistency between your external file system and slack, allowing you to remove files that are no longer relevant or have been deleted from the source. the removal can be performed using either the creator-defined external id or slack's internal file id. this operation only removes the file reference from slack and does not affect the original file in its external location.

**Action Parameters**

<ParamField path="external_id" type="string">
</ParamField>

<ParamField path="file" type="string">
</ParamField>

<ParamField path="token" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REMOVE_A_STAR_FROM_AN_ITEM">
Removes a star from an item (message, file, file comment, channel, private group, or direct message) on behalf of the authenticated user. this endpoint is used when a user wants to unstar content in slack. it requires at least one parameter to identify the item to be unstarred. the method broadcasts a 'star removed' event through the rtm api for the calling user. use this endpoint to programmatically manage starred items in a user's slack workspace.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="file" type="string">
</ParamField>

<ParamField path="file_comment" type="string">
</ParamField>

<ParamField path="timestamp" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REMOVE_A_USER_FROM_A_CONVERSATION">
The conversations.kick endpoint removes a specified user from a slack conversation (channel). this method is used when you need to forcibly remove a member from a channel, which can be useful for moderation or managing channel membership. it requires the necessary permissions based on the type of channel being used. the endpoint should be used judiciously, as kicking a user may impact team dynamics. note that this method cannot be used to remove oneself from a channel; for that purpose, use the conversations.leave method instead. be aware that the kicked user will be able to rejoin the channel if it's a public channel unless further restrictions are put in place.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="user" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REMOVE_CHANNELS_FROM_IDP_GROUP">
Removes specified channels from a designated user group in slack. this endpoint allows administrators to disassociate multiple channels from a user group simultaneously, which can be useful for managing access control and organization within a slack workspace. it should be used when you need to update the channel associations for a user group, such as when reorganizing team structures or adjusting access permissions. note that this operation only removes the association between the channels and the user group; it does not delete the channels or the user group itself. be cautious when using this endpoint, as it may impact users' access to channels they previously had through the user group membership.

**Action Parameters**

<ParamField path="channel_ids" type="string" required={true}>
</ParamField>

<ParamField path="usergroup_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REMOVE_ENTERPRISE_GRID_ORG_EMOJI">
Removes a custom emoji from a slack enterprise grid organization. this endpoint allows administrators to delete a specific custom emoji, making it unavailable across all workspaces within the organization. it should be used when you need to remove inappropriate, outdated, or unused custom emojis. the removal is permanent and affects all users in the enterprise grid. this tool does not provide a way to backup or archive the removed emoji, so use it cautiously.

**Action Parameters**

<ParamField path="name" type="string" required={true}>
</ParamField>

<ParamField path="token" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REMOVE_IDP_GROUP_FROM_PRIVATE_CHANNEL">
This endpoint removes an identity provider (idp) group from the access list of a private slack channel. it's used to revoke access for all members of the specified idp group to a particular private channel. this action is part of slack's advanced access management features, allowing administrators to fine-tune channel access based on organizational structure. use this endpoint when you need to adjust access permissions for large groups of users simultaneously, based on their idp group membership. it's particularly useful for maintaining security and information compartmentalization in enterprise environments. note that this endpoint only affects private channels and requires admin-level permissions to execute.

**Action Parameters**

<ParamField path="channel_id" type="string" required={true}>
</ParamField>

<ParamField path="group_id" type="string" required={true}>
</ParamField>

<ParamField path="team_id" type="string" required={true}>
</ParamField>

<ParamField path="token" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REMOVE_REACTION_FROM_ITEM">
The reactions.remove endpoint allows you to remove a specific reaction (emoji) from a message, file, or file comment in slack. this method is useful for undoing reactions or cleaning up emoji responses on various items within slack channels. to use this endpoint, you must specify the reaction name and the item (message, file, or file comment) from which to remove the reaction. for messages, both the channel and timestamp are required. for files or file comments, you need to provide the respective file or file comment id. this endpoint is particularly helpful for moderation purposes or when managing automated reactions in integrated applications.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="file" type="string">
</ParamField>

<ParamField path="file_comment" type="string">
</ParamField>

<ParamField path="name" type="string" required={true}>
</ParamField>

<ParamField path="timestamp" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REMOVE_USER_FROM_WORKSPACE">
The admin.users.remove endpoint allows workspace administrators to remove a specific user from a slack workspace. this action permanently deletes the user's access to the workspace and all associated channels. it should be used when an employee leaves the organization or when their access needs to be revoked for security reasons. the endpoint requires both the workspace id and the user id to ensure precise user removal. it's important to note that this action is irreversible, so it should be used with caution. this endpoint does not handle transferring the user's data or files before removal, so any necessary data preservation should be done separately before calling this endpoint.

**Action Parameters**

<ParamField path="team_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RENAME_AN_EMOJI">
Renames a custom emoji in a slack workspace. this endpoint allows administrators to change the name of an existing custom emoji, which can be useful for organizing emojis or correcting naming errors. the function requires admin-level permissions and should be used when there's a need to update emoji names without removing and re-adding them. it's important to note that this action will affect all instances of the emoji across the workspace, so it should be used with caution to avoid disrupting existing communications that may reference the emoji by its old name.

**Action Parameters**

<ParamField path="name" type="string" required={true}>
</ParamField>

<ParamField path="new_name" type="string" required={true}>
</ParamField>

<ParamField path="token" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RENAME_A_CONVERSATION">
This endpoint renames an existing conversation (channel) in slack. it should be used when you need to update the name of a channel, either for rebranding, clarifying its purpose, or organizing workspace structure. the endpoint requires the unique channel id and the desired new name. only authorized users (such as the channel creator, workspace admins, or channel managers) can perform this action. the new name must comply with slack's naming conventions, and the api will automatically adjust it if necessary. note that renaming a channel may affect existing integrations or saved references to the channel name.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="name" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RENAME_A_SLACK_CHANNEL">
Renames a public or private channel in a slack workspace. this endpoint should be used when an admin or authorized user needs to change the name of an existing channel. it's particularly useful for keeping channel names up-to-date with team or project changes. the endpoint only modifies the channel name and does not affect its members, purpose, or any other attributes. note that this action is only available to workspace admins or users with the channel manager role in enterprise grid workspaces.

**Action Parameters**

<ParamField path="channel_id" type="string" required={true}>
</ParamField>

<ParamField path="name" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REQUEST_ADDITIONAL_APP_PERMISSIONS">
The 'apps.permissions.request' endpoint allows slack app developers to request additional permissions for their application. this method is used when an app needs to expand its capabilities and requires new scopes to access additional slack workspace features or data. it initiates the process of requesting new permissions, which typically involves notifying workspace administrators for approval. this endpoint should be used cautiously and only when necessary, as frequent permission changes may impact user trust. it's important to note that calling this endpoint does not automatically grant new permissions; it only starts the request process.

**Action Parameters**

<ParamField path="scopes" type="string" required={true}>
</ParamField>

<ParamField path="trigger_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RESTRICT_APP_INSTALLATION_ON_WORKSPACE">
Restricts a specified app within a slack workspace, limiting its access or functionality. this endpoint should be used by workspace administrators when they need to prevent or control the use of a particular app due to security concerns, compliance requirements, or organizational policies. it allows for granular control over app permissions and usage within the team. the restriction is applied based on the provided app id, request id, and team id, ensuring precise targeting of the restriction action. this tool is essential for maintaining workspace security and enforcing app usage policies but should be used judiciously to avoid disrupting workflow integrations.

**Action Parameters**

<ParamField path="app_id" type="string">
</ParamField>

<ParamField path="request_id" type="string">
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RETRIEVE_A_USER_S_IDENTITY_DETAILS">
The users.identity endpoint retrieves the identity of a slack user associated with the provided token. it allows applications to verify and access basic information about the authenticated user. this endpoint is particularly useful when you need to confirm the identity of a user interacting with your slack app or integration. it should be used when you require up-to-date user information or need to validate a user's identity within your application's workflow. the endpoint returns basic user details but does not provide extensive profile information or team-wide data. note that the specific user information returned may depend on the scopes granted to your application during the oauth process.

**Action Parameters**

**Action Response**

<ParamField path="data" type="array">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RETRIEVE_CALL_INFORMATION">
The calls.info endpoint retrieves detailed information about a specific call in a slack workspace. it allows developers to access comprehensive data about a call, including its participants, duration, status, and any associated metadata. this endpoint is particularly useful for applications that need to track or analyze call activity within slack, such as reporting tools or integration with other communication platforms. it should be used when detailed information about a specific call is required, rather than for listing multiple calls or initiating new calls. the endpoint returns a snapshot of the call data at the time of the request and does not provide real-time updates or streaming capabilities.

**Action Parameters**

<ParamField path="id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RETRIEVE_CONVERSATION_INFORMATION">
The conversations.info endpoint retrieves detailed information about a specific conversation in slack. it provides comprehensive data about channels, direct messages, or multi-person direct messages, including their properties, members, and settings. this endpoint is particularly useful when you need to access metadata about a conversation, such as its name, purpose, creation date, or membership details. it should be used when detailed information about a specific slack conversation is required, rather than for listing multiple conversations or accessing message content. note that while this endpoint provides extensive metadata, it does not return the actual messages within the conversation; for that, you would need to use a separate endpoint like conversations.history.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="include_locale" type="boolean">
</ParamField>

<ParamField path="include_num_members" type="boolean">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RETRIEVE_CONVERSATION_MEMBERS_LIST">
Retrieves a list of members in a specific slack conversation. this endpoint is part of the conversations api and can be used to fetch member information for public channels, private channels, direct messages, and group direct messages. it's particularly useful for analyzing conversation participants, managing large channels, or integrating member information into external systems. the endpoint supports pagination for efficiently handling conversations with a large number of members. note that the api may not return members who have left the conversation or workspace, focusing only on current active members.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RETRIEVE_CURRENT_USER_DND_STATUS">
Retrieves information about a user's current do not disturb (dnd) status in slack. this endpoint allows you to check if a user has dnd enabled, and if so, it likely provides details such as when the dnd period started and when it's scheduled to end. use this endpoint when you need to determine a user's availability or to respect their notification preferences before sending messages. it's particularly useful for applications that need to manage communication timing or display user status. note that this endpoint does not modify the dnd status; it's for information retrieval only.

**Action Parameters**

<ParamField path="user" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RETRIEVE_DETAILED_INFORMATION_ABOUT_A_FILE">
The files.info endpoint retrieves detailed metadata about a specific file in slack. it allows you to fetch comprehensive information about a file, including its properties, sharing status, and associated data. this endpoint is particularly useful when you need to access or display detailed file information within your application or integration. the method returns a file object containing various attributes such as file type, size, creation date, and sharing details. it's important to note that this endpoint only provides metadata and does not download the actual file content.

**Action Parameters**

<ParamField path="count" type="string">
</ParamField>

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="file" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="page" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RETRIEVE_DETAILED_USER_INFORMATION">
The users.info endpoint retrieves detailed information about a specific user in a slack workspace. it returns comprehensive profile data including the user's name, email, role, status, and other relevant details. this endpoint is particularly useful when you need to fetch up-to-date information about a user, such as checking their current status, verifying their role, or accessing their contact information. it should be used when detailed user data is required for user management, profile display, or integration with other systems. note that while this endpoint provides extensive user information, it does not return data about the user's message history or channel memberships.

**Action Parameters**

<ParamField path="include_locale" type="boolean">
</ParamField>

<ParamField path="user" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RETRIEVE_MESSAGE_PERMALINK_URL">
Retrieves a permalink url for a specific message in a slack channel. this endpoint is used when you need to generate a shareable link to a particular message, allowing easy reference and access to the message content. the permalink can be used to quickly navigate to the exact message within the slack interface, even if it's part of a thread or in a private channel (provided the user has appropriate permissions). this method is particularly useful for creating links to important messages, sharing context in discussions, or integrating slack message references into other tools or documentation. note that the generated permalink respects slack's privacy settings, so users will only be able to access the message if they have the necessary permissions in the slack workspace.

**Action Parameters**

<ParamField path="channel" type="string" required={true}>
</ParamField>

<ParamField path="message_ts" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RETRIEVE_REMOTE_FILE_INFO_IN_SLACK">
Retrieves detailed information about a remote file that has been added to slack. this endpoint is used when you need to access metadata and current status of a file that exists externally but has been registered with slack. it's particularly useful for tracking the sharing status, permissions, and other slack-specific attributes of the remote file. the endpoint doesn't download or modify the file itself; it only provides information about how the file is represented within slack. use this when you need to verify the file's current state in slack or before performing operations like updating or sharing the remote file.

**Action Parameters**

<ParamField path="external_id" type="string">
</ParamField>

<ParamField path="file" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RETRIEVE_TEAM_PROFILE_DETAILS">
Retrieves the profile information for a slack team, including both custom and default profile fields. this endpoint is useful for obtaining comprehensive details about a team's profile structure, such as field definitions, labels, and visibility settings. it can be used to gather information for team management, integration purposes, or to populate user interfaces with team-specific data. the endpoint does not modify any team data and is read-only, making it safe for frequent calls to keep local data in sync with slack's servers.

**Action Parameters**

<ParamField path="visibility" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_RETRIEVE_USER_PROFILE_INFORMATION">
The users.profile.get endpoint retrieves the profile information of a slack user. it allows you to fetch details such as display name, status, profile picture, and custom fields associated with a user's profile. this endpoint is particularly useful when you need to access up-to-date information about a specific user or the authenticated user. the retrieved data can be used for various purposes, such as populating user information in integrated applications or displaying user details in custom interfaces. note that while the endpoint provides comprehensive profile information, it does not include data about the user's participation in channels or their message history.

**Action Parameters**

<ParamField path="include_labels" type="boolean">
</ParamField>

<ParamField path="user" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REVERSE_A_CONVERSATION_S_ARCHIVAL_STATUS">
The 'conversations.unarchive' endpoint allows you to restore an archived conversation (channel) in slack, making it active and accessible again. this method is useful when you need to reactivate a previously archived channel for continued use. it's particularly handy for teams that temporarily archive channels for organizational purposes or to reduce clutter, but later decide to resume activities in that conversation. the endpoint requires specifying the exact channel id to be unarchived. note that this operation can only be performed on channels that are currently in an archived state; attempting to unarchive an active channel will result in an error. also, be aware that unarchiving a channel may affect channel limits for your slack workspace, depending on your current plan and usage.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REVOKES_A_TOKEN_WITH_OPTIONAL_TEST_MODE">
Revokes an access token, effectively invalidating it for future use. this endpoint should be called when an application needs to programmatically revoke a token, such as during user logout or when a token is compromised. it's a crucial security measure for managing token lifecycles. the revocation is immediate and permanent; once revoked, the token cannot be reactivated. this endpoint doesn't return the revoked token or any user information for security reasons.

**Action Parameters**

<ParamField path="test" type="boolean">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_REVOKE_PUBLIC_SHARING_ACCESS_FOR_A_FILE">
The 'files.revokepublicurl' endpoint revokes public and external sharing access for a specific file in slack. use this endpoint when you need to disable public sharing of a previously shared file, enhancing privacy and control over sensitive information. this method is particularly useful for situations where a file was accidentally made public or when the sharing period needs to end. the endpoint returns a file object containing updated sharing information, allowing you to confirm the revocation was successful. note that this action is irreversible, and you'll need to create a new public url if sharing is required again in the future.

**Action Parameters**

<ParamField path="file" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SCHEDULES_A_MESSAGE_TO_A_CHANNEL_AT_A_SPECIFIED_TIME">
The chat.schedulemessage endpoint allows you to schedule a message for future delivery in a slack channel, direct message, or private group. this powerful feature enables automated, time-based communication within your slack workspace. use this endpoint when you need to send messages at specific times, such as reminders, announcements, or recurring updates. the scheduled message can include rich formatting using attachments or blocks, and supports various customization options like threading and link unfurling. keep in mind that scheduled messages are typically limited to 120 days in the future, and the scheduling user must have the necessary permissions in the target channel.

**Action Parameters**

<ParamField path="as_user" type="boolean">
</ParamField>

<ParamField path="attachments" type="string">
</ParamField>

<ParamField path="blocks" type="string">
</ParamField>

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="link_names" type="boolean">
</ParamField>

<ParamField path="parse" type="string">
</ParamField>

<ParamField path="post_at" type="string">
</ParamField>

<ParamField path="reply_broadcast" type="boolean">
</ParamField>

<ParamField path="text" type="string">
</ParamField>

<ParamField path="thread_ts" type="integer">
</ParamField>

<ParamField path="unfurl_links" type="boolean">
</ParamField>

<ParamField path="unfurl_media" type="boolean">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SEARCH_CHANNELS_IN_AN_ENTERPRISE_ORGANIZATION">
The admin.conversations.search endpoint allows slack workspace administrators to search for conversations across their organization. it enables finding specific channels based on criteria like team membership, names, or topics. this tool is useful for managing large enterprises with multiple teams, helping admins organize their slack workspace effectively. it supports pagination and filtering, making it ideal for auditing channel usage or locating specific project-related channels.

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="limit" type="integer">
</ParamField>

<ParamField path="query" type="string">
</ParamField>

<ParamField path="search_channel_types" type="string">
</ParamField>

<ParamField path="sort" type="string">
</ParamField>

<ParamField path="sort_dir" type="string">
</ParamField>

<ParamField path="team_ids" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SEARCH_FOR_MESSAGES_WITH_QUERY">
The search.messages endpoint allows you to search for messages across all channels, direct messages, and private groups in a slack workspace. it provides powerful search capabilities to find specific content within conversations. this endpoint is particularly useful for retrieving historical information, locating important discussions, or building search functionality into slack-integrated applications. the search results include message content, associated metadata, and can be refined using various parameters for sorting, highlighting, and pagination. keep in mind that the search may not include very recent messages due to indexing delays, and there may be rate limits on the number of requests you can make in a given time period.

**Action Parameters**

<ParamField path="count" type="integer" default="1">
</ParamField>

<ParamField path="highlight" type="boolean">
</ParamField>

<ParamField path="page" type="integer">
</ParamField>

<ParamField path="query" type="string" required={true}>
</ParamField>

<ParamField path="sort" type="string">
</ParamField>

<ParamField path="sort_dir" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SEARCH_MESSAGES">
The search.messages endpoint allows you to search for messages across all channels, direct messages, and private groups in a slack workspace. it provides powerful search capabilities to find specific content within conversations. this endpoint is particularly useful for retrieving historical information, locating important discussions, or building search functionality into slack-integrated applications. the search results include message content, associated metadata, and can be refined using various parameters for sorting, highlighting, and pagination. keep in mind that the search may not include very recent messages due to indexing delays, and there may be rate limits on the number of requests you can make in a given time period.(DEPRECATED use search_for_messages_with_query)

**Action Parameters**

<ParamField path="count" type="integer" default="1">
</ParamField>

<ParamField path="highlight" type="boolean">
</ParamField>

<ParamField path="page" type="integer">
</ParamField>

<ParamField path="query" type="string" required={true}>
</ParamField>

<ParamField path="sort" type="string">
</ParamField>

<ParamField path="sort_dir" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SENDS_A_MESSAGE_TO_A_SLACK_CHANNEL">
The chat.postmessage endpoint allows you to send a message to a slack channel or user. this versatile tool can be used to post simple text messages, create rich interactive messages with blocks or attachments, initiate or reply to threads, and customize the appearance of bot messages. it's ideal for sending notifications, updates, or interactive content to slack users or channels programmatically. the endpoint supports extensive customization of message content and appearance, making it suitable for a wide range of use cases from simple notifications to complex interactive workflows. however, it's important to note that the message structure and appearance may vary depending on the slack client and the parameters used.

**Action Parameters**

<ParamField path="as_user" type="boolean">
</ParamField>

<ParamField path="attachments" type="string">
</ParamField>

<ParamField path="blocks" type="string">
</ParamField>

<ParamField path="channel" type="string" required={true}>
</ParamField>

<ParamField path="icon_emoji" type="string">
</ParamField>

<ParamField path="icon_url" type="string">
</ParamField>

<ParamField path="link_names" type="boolean">
</ParamField>

<ParamField path="mrkdwn" type="boolean">
</ParamField>

<ParamField path="parse" type="string">
</ParamField>

<ParamField path="reply_broadcast" type="boolean">
</ParamField>

<ParamField path="text" type="string">
</ParamField>

<ParamField path="thread_ts" type="string">
</ParamField>

<ParamField path="unfurl_links" type="boolean">
</ParamField>

<ParamField path="unfurl_media" type="boolean">
</ParamField>

<ParamField path="username" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SENDS_EPHEMERAL_MESSAGES_TO_CHANNEL_USERS">
The 'chat.postephemeral' endpoint allows you to send an ephemeral message to a specific user within a slack channel. ephemeral messages are temporary and only visible to the specified user, making them ideal for personalized notifications, alerts, or responses that don't need to be seen by the entire channel. this endpoint is particularly useful for interactive workflows, providing user-specific information, or sending sensitive data that shouldn't be visible to all channel members. the message can be customized with various formatting options, including attachments and block kit elements, allowing for rich, interactive content. keep in mind that ephemeral messages are not permanently stored and cannot be retrieved once they disappear, so they should not be used for critical information that needs to be referenced later.

**Action Parameters**

<ParamField path="as_user" type="boolean">
</ParamField>

<ParamField path="attachments" type="string">
</ParamField>

<ParamField path="blocks" type="string">
</ParamField>

<ParamField path="channel" type="string" required={true}>
</ParamField>

<ParamField path="icon_emoji" type="string">
</ParamField>

<ParamField path="icon_url" type="string">
</ParamField>

<ParamField path="link_names" type="boolean">
</ParamField>

<ParamField path="parse" type="string">
</ParamField>

<ParamField path="text" type="string">
</ParamField>

<ParamField path="thread_ts" type="string">
</ParamField>

<ParamField path="user" type="string" required={true}>
</ParamField>

<ParamField path="username" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SET_A_CONVERSATION_S_PURPOSE">
The 'conversations.setpurpose' endpoint allows you to set or update the purpose (description) of a slack conversation. this function is useful for providing context and clarity about the intended use of a channel, group, or direct message. use this endpoint when you need to establish or modify the purpose of a conversation, which can help team members understand its focus and relevance. the endpoint requires specifying the conversation id and the new purpose text. it's important to note that the calling user must be a member of the conversation to set its purpose, and the new purpose is subject to slack's character limit restrictions.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="purpose" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SET_CHANNEL_POSTING_PERMISSIONS">
Sets or updates the preferences for a specific slack conversation (channel). this endpoint allows administrators to configure various settings for a channel, such as who can post messages, whether threads can be used, or if huddles are allowed. it's particularly useful for managing large workspaces or enforcing specific policies across different channels. the endpoint should be used when there's a need to programmatically adjust channel settings, especially in bulk operations or when integrating with other management tools. note that this endpoint requires administrative privileges and may not be available to all users or in all slack plans.

**Action Parameters**

<ParamField path="channel_id" type="string" required={true}>
</ParamField>

<ParamField path="prefs" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SET_EXPIRATION_FOR_A_GUEST_USER">
Sets an expiration date for a guest user account in a slack workspace. this endpoint allows workspace administrators to automatically disable guest access after a specified time, enhancing security and access control. it should be used when granting temporary access to external collaborators or for time-limited projects. the expiration is set using a unix timestamp, allowing precise control over when the guest account will be disabled. this method only applies to guest accounts and cannot be used to set expirations for regular workspace members or multi-channel guests.

**Action Parameters**

<ParamField path="expiration_ts" type="integer" required={true}>
</ParamField>

<ParamField path="team_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SET_READ_CURSOR_IN_A_CONVERSATION">
The 'conversations.mark' endpoint sets the read cursor in a slack channel or conversation, marking a specific message as the last one read by the user. this function is essential for managing unread message indicators and tracking user engagement within slack conversations. use this endpoint when you need to programmatically update a user's read position, such as when implementing custom slack clients or managing user interactions in integrated applications. the endpoint is particularly useful for maintaining accurate scroll-back history in busy channels or for synchronizing read status across multiple devices. note that this action only affects the calling user's read cursor and does not impact other members of the conversation.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="ts" type="integer">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SET_SLACK_USER_PROFILE_INFORMATION">
The users.profile.set endpoint allows updating a user's profile information in slack. it can be used to modify both default profile fields (such as first name, last name, and email) and custom profile fields. this endpoint offers flexibility in updating either multiple fields at once or a single field, making it suitable for various profile management scenarios. team administrators on paid slack teams can use this endpoint to update profiles of other team members by specifying the 'user' parameter. the endpoint is particularly useful for keeping user information up-to-date, managing custom fields, and automating profile updates in integrated workflows. however, it's important to note the limitation of updating a maximum of 50 fields at once, and each field name is restricted to 255 characters.

**Action Parameters**

<ParamField path="name" type="string">
</ParamField>

<ParamField path="profile" type="string">
</ParamField>

<ParamField path="user" type="string">
</ParamField>

<ParamField path="value" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SET_THE_TOPIC_OF_A_CONVERSATION">
Sets or updates the topic for a specified slack conversation. this endpoint allows you to change the topic of any type of slack conversation, including public channels, private channels, direct messages, and group conversations. the topic is a brief description or subject line that appears at the top of the conversation, helping team members understand its purpose or current focus. use this when you need to programmatically update or set a conversation's topic. note that the new topic does not support any text formatting or automatic link creation (linkification). the authenticated user must have the necessary permissions to modify the topic in the specified conversation.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="topic" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SET_USER_PROFILE_PHOTO_WITH_CROPPING_OPTIONS">
The users.setphoto endpoint allows users to upload and set their profile photo in slack. this method accepts an image file via multipart/form-data and provides optional cropping functionality. use this endpoint when a user wants to change their profile picture or when an application needs to programmatically update a user's photo. the endpoint supports various image formats and automatically resizes large images. if cropping is desired, all three cropping parameters (crop x, crop y, crop w) must be provided together for precise photo customization.

**Action Parameters**

<ParamField path="crop_w" type="string">
</ParamField>

<ParamField path="crop_x" type="string">
</ParamField>

<ParamField path="crop_y" type="string">
</ParamField>

<ParamField path="image" type="string">
</ParamField>

<ParamField path="token" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SET_USER_TO_REGULAR_STATUS">
This endpoint converts a user's role to a regular user within a specific slack enterprise grid workspace. it can be used to downgrade the permissions of guests, admins, or owners to standard user privileges. the endpoint is specifically designed for enterprise grid organizations and should be used when there's a need to adjust user roles for organizational management purposes. it's important to note that this action can have significant impacts on user permissions and should be used carefully. the endpoint doesn't provide information about the user's previous role or confirm the success of the role change in the response.

**Action Parameters**

<ParamField path="team_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SET_WORKSPACE_CONNECTIONS_FOR_A_CHANNEL">
The admin.conversations.setteams endpoint allows slack enterprise grid administrators to manage the workspace associations of a specific channel. it enables sharing a channel across multiple workspaces or converting it to an org-wide channel visible to the entire enterprise grid organization. this endpoint is crucial for facilitating cross-workspace collaboration and centralizing important channels. it should be used when there's a need to expand a channel's reach beyond its original workspace or to streamline communication across the organization. however, it's important to note that this endpoint requires administrative privileges and should be used cautiously to maintain proper channel organization and access control.

**Action Parameters**

<ParamField path="channel_id" type="string" required={true}>
</ParamField>

<ParamField path="org_channel" type="boolean">
</ParamField>

<ParamField path="target_team_ids" type="string">
</ParamField>

<ParamField path="team_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SET_WORKSPACE_DEFAULT_CHANNELS">
Sets default channels for a specified slack workspace. this endpoint allows workspace administrators to define which public channels new members will automatically join when they are added to the workspace. it's useful for ensuring new members have immediate access to important company-wide information channels. the endpoint should be used when updating onboarding processes or reorganizing workspace structure. note that only public channels can be set as default, and this action requires administrative privileges.

**Action Parameters**

<ParamField path="channel_ids" type="string" required={true}>
</ParamField>

<ParamField path="team_id" type="string" required={true}>
</ParamField>

<ParamField path="token" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SET_WORKSPACE_DESCRIPTION">
Sets a new description for a specified slack workspace. this endpoint allows workspace administrators to update the team's description, which is typically visible to all members and helps convey the purpose or focus of the workspace. use this when you need to modify the existing workspace description or set an initial description for a newly created workspace. the endpoint is particularly useful for maintaining up-to-date information about the workspace, especially after organizational changes or shifts in team focus. note that this operation will overwrite any existing description, so ensure you have the full desired text ready before making the api call.

**Action Parameters**

<ParamField path="description" type="string" required={true}>
</ParamField>

<ParamField path="team_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SET_WORKSPACE_DISCOVERABILITY_SETTINGS">
Sets the discoverability setting for a specific slack workspace within an enterprise grid organization. this endpoint allows administrators to control the visibility and accessibility of a workspace to external users. it should be used when you need to change how a workspace can be discovered or joined by potential new members. the endpoint requires both the workspace id and the desired discoverability setting. it's important to note that this action can have significant implications for workspace access and should be used carefully, considering the organization's security and collaboration needs.

**Action Parameters**

<ParamField path="discoverability" type="string" required={true}>
</ParamField>

<ParamField path="team_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SET_WORKSPACE_ICON">
Sets the icon for a specific slack workspace within an enterprise grid organization. this endpoint allows administrators to update the visual identity of a workspace by providing a new icon image url. it's particularly useful for maintaining brand consistency across multiple workspaces or updating the appearance of a specific team's workspace. this method is exclusive to enterprise grid workspaces and requires appropriate administrative permissions. use this endpoint when you need to programmatically update workspace icons, such as during workspace setup automation or brand refresh initiatives.

**Action Parameters**

<ParamField path="image_url" type="string" required={true}>
</ParamField>

<ParamField path="team_id" type="string" required={true}>
</ParamField>

<ParamField path="token" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SET_WORKSPACE_NAME">
The admin.teams.settings.setname endpoint allows workspace owners or administrators to change the name of a slack workspace. this operation updates the workspace name displayed in menus, headings, and other areas throughout the slack interface. use this endpoint when you need to rebrand a workspace, reflect organizational changes, or simply update the workspace name for clarity. the new name will be visible to all members of the workspace immediately after the change. note that this action may affect integrations or scripts that rely on the workspace name, so it should be used cautiously and communicated to team members in advance. this endpoint requires administrative privileges and should only be used by authorized personnel with the appropriate permissions.

**Action Parameters**

<ParamField path="name" type="string" required={true}>
</ParamField>

<ParamField path="team_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SHARE_A_ME_MESSAGE_IN_A_CHANNEL">
The chat.memessage endpoint allows you to send a "me message" to a specified slack channel. a "me message" is a special type of message that appears as an action performed by the user, typically used for status updates or to describe actions in the third person. this endpoint is useful when you want to share status updates, mood, or actions in a more expressive and visual way than a standard message. it should be used when you want to simulate irc-style /me messages in slack. note that while this endpoint provides a unique way to communicate, it doesn't support rich text formatting or attachments like standard messages do.

**Action Parameters**

<ParamField path="channel" type="string">
</ParamField>

<ParamField path="text" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_SHARE_REMOTE_FILE_IN_CHANNELS">
Shares a previously added remote file into one or more slack channels or conversations. this endpoint allows you to make a remote file visible in specific slack channels without re-uploading the file. it's useful for sharing files that are stored in external services but need to be accessible within slack conversations. the file must have been added to slack beforehand using the 'files.remote.add' method. this endpoint should be used when you want to distribute a remote file to multiple channels or threads efficiently. it does not support sharing files that were directly uploaded to slack.

**Action Parameters**

<ParamField path="channels" type="string">
</ParamField>

<ParamField path="external_id" type="string">
</ParamField>

<ParamField path="file" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_START_REAL_TIME_MESSAGING_SESSION">
The rtm.connect endpoint initiates a websocket-based real-time messaging (rtm) session with slack. it provides a websocket url and initial workspace metadata, allowing applications to establish a real-time connection for receiving events and sending messages. this endpoint should be used when implementing real-time features such as live updates or instant messaging within a slack integration. the connection is single-use and valid for only 30 seconds, so applications must act quickly to establish the websocket connection. it's important to note that this method doesn't support passing a presence value, unlike its counterpart rtm.start.

**Action Parameters**

<ParamField path="batch_presence_aware" type="boolean">
</ParamField>

<ParamField path="presence_sub" type="boolean">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_TRIGGER_USER_PERMISSIONS_MODAL">
The apps.permissions.users.request endpoint initiates a request for additional user-level permissions for a slack app. this method is used when an app needs to expand its access to user data or actions within a slack workspace. it's particularly useful for apps that require dynamic permission scaling based on user interactions or evolving feature sets. the endpoint likely triggers a permission request prompt for the user, allowing them to grant or deny the additional permissions. note that this endpoint doesn't directly grant permissions; it only starts the request process. the actual permission changes would occur after user approval through the oauth flow. use this endpoint cautiously and only when absolutely necessary to maintain user trust and adhere to privacy best practices.

**Action Parameters**

<ParamField path="scopes" type="string" required={true}>
</ParamField>

<ParamField path="trigger_id" type="string" required={true}>
</ParamField>

<ParamField path="user" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_UNARCHIVE_A_PUBLIC_OR_PRIVATE_CHANNEL">
Unarchives a previously archived slack channel, making it active and accessible to users again. this endpoint should be used when an administrator needs to restore a channel that was previously archived, allowing team members to resume conversations and access shared content. it's particularly useful for reactivating seasonal or project-based channels that may have been temporarily archived. note that this action is only available to administrators in enterprise grid or business+ organizations and requires specific administrative permissions. the endpoint does not provide information about the channel's content or members; it simply changes the channel's status from archived to active.

**Action Parameters**

<ParamField path="channel_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_UNINSTALL_APP_FROM_WORKSPACE">
The apps.uninstall endpoint allows you to remove a slack app from a workspace or an entire enterprise organization. this method revokes all tokens associated with a single installation of an app, effectively uninstalling it. use this endpoint when you need to programmatically remove a slack app, either due to user request, compliance reasons, or as part of app lifecycle management. it's particularly useful for administrators and app developers who need to manage app installations across multiple workspaces. note that uninstalling an app will immediately terminate its access and functionality within the slack environment, so use this method with caution.

**Action Parameters**

<ParamField path="client_id" type="string">
</ParamField>

<ParamField path="client_secret" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_UNPIN_ITEM_FROM_CHANNEL">
The 'pins.remove' endpoint allows you to remove a pinned message from a specified slack channel. this function is useful for decluttering important information in a channel or removing outdated pinned content. it requires specifying the channel where the pinned item is located and optionally, the timestamp of the specific message to be un-pinned. if the timestamp is not provided, the behavior may default to removing the most recently pinned item. use this endpoint when you need to programmatically manage pinned content in slack channels, such as in automation workflows or when maintaining channel organization. note that this action cannot be undone, so use it cautiously.

**Action Parameters**

<ParamField path="channel" type="string" required={true}>
</ParamField>

<ParamField path="timestamp" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_UPDATES_AN_EXISTING_REMOTE_FILE">
The 'files.remote.update' endpoint allows you to update the metadata and content of remote files that have been shared in slack. this tool is useful for keeping shared file information up-to-date, such as modifying titles, updating urls, or refreshing preview images. it can be used when file details change externally or when you need to enhance the file's representation within slack. the endpoint doesn't upload new files but rather updates existing remote file references. note that while you can update various attributes, you cannot change the fundamental nature of the file (e.g., converting from one file type to another) through this endpoint.

**Action Parameters**

<ParamField path="external_id" type="string">
</ParamField>

<ParamField path="external_url" type="string">
</ParamField>

<ParamField path="file" type="string">
</ParamField>

<ParamField path="filetype" type="string">
</ParamField>

<ParamField path="indexable_file_contents" type="string">
</ParamField>

<ParamField path="preview_image" type="string">
</ParamField>

<ParamField path="title" type="string">
</ParamField>

<ParamField path="token" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_UPDATES_A_SLACK_MESSAGE">
The chat.update endpoint allows you to modify an existing message in a slack channel. it's used when you need to update the content, attachments, or formatting of a previously sent message. this can be particularly useful for updating dynamic content, correcting errors, or providing real-time updates to information. the endpoint requires specifying both the channel and the timestamp of the message to be updated. it offers flexible options for updating the message text, attachments, and blocks, allowing for rich formatting and interactive elements. however, it's important to note that this endpoint can't be used to update messages sent by other users or apps, and there may be time limitations on how long after a message is sent that it can be updated.

**Action Parameters**

<ParamField path="as_user" type="string">
</ParamField>

<ParamField path="attachments" type="string">
</ParamField>

<ParamField path="blocks" type="string">
</ParamField>

<ParamField path="channel" type="string" required={true}>
</ParamField>

<ParamField path="link_names" type="string">
</ParamField>

<ParamField path="parse" type="string">
</ParamField>

<ParamField path="text" type="string">
</ParamField>

<ParamField path="ts" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_UPDATE_AN_EXISTING_SLACK_USER_GROUP">
The usergroups.update endpoint allows you to modify an existing user group within a slack workspace. it enables updating various properties of a user group, including its name, description, mention handle, and default channels. this endpoint is particularly useful when you need to adjust user group details to reflect changes in team structure, communication needs, or organizational shifts. use it to keep user groups current and relevant, ensuring efficient team management and targeted communication. the endpoint requires the user group id and allows optional updates to other properties, providing flexibility in managing group characteristics without recreating the entire group.

**Action Parameters**

<ParamField path="channels" type="string">
</ParamField>

<ParamField path="description" type="string">
</ParamField>

<ParamField path="handle" type="string">
</ParamField>

<ParamField path="include_count" type="boolean">
</ParamField>

<ParamField path="name" type="string">
</ParamField>

<ParamField path="usergroup" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_UPDATE_AN_EXISTING_SLACK_VIEW">
The views.update method is used to update an existing modal view in slack. it allows you to dynamically modify the content and structure of a modal that is already open for a user. this method is particularly useful for updating interactive components within a modal based on user actions or external events. to use this method, you must provide either the view id or external id to identify the specific view to be updated, along with a new view definition object. the optional hash parameter can be used to prevent race conditions when updating views asynchronously.

**Action Parameters**

<ParamField path="external_id" type="string">
</ParamField>

<ParamField path="hash" type="string">
</ParamField>

<ParamField path="view" type="string">
</ParamField>

<ParamField path="view_id" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_UPDATE_SLACK_CALL_INFORMATION">
The 'calls.update' endpoint allows you to modify the details of an existing call in slack. this method is used to update call information such as the title, join url, and desktop app join url. it's particularly useful for keeping call information current, especially when call details change after initial creation. the endpoint requires the unique call id and allows optional updates to the call's title and access urls. this method is essential for maintaining accurate and up-to-date call information within slack, enhancing user experience and ensuring smooth integration with third-party call services. note that this endpoint only updates the specified fields and doesn't affect other call properties not included in the request.

**Action Parameters**

<ParamField path="desktop_app_join_url" type="string">
</ParamField>

<ParamField path="id" type="string" required={true}>
</ParamField>

<ParamField path="join_url" type="string">
</ParamField>

<ParamField path="title" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_UPDATE_USER_GROUP_MEMBERS">
Updates the list of users in a slack user group. this endpoint allows you to completely replace the existing list of users in a specified user group with a new set of users. it's useful for bulk updates to group membership, such as when syncing with an external user management system or reorganizing teams. the endpoint requires the user group id and a full list of user ids to be included in the group. optionally, you can request a count of users in the group after the update. note that this operation will overwrite the existing user list, so ensure you include all desired users in the update.

**Action Parameters**

<ParamField path="include_count" type="boolean">
</ParamField>

<ParamField path="usergroup" type="string" required={true}>
</ParamField>

<ParamField path="users" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_UPDATE_WORKFLOW_EXTENSION_STEP_CONFIGURATION">
The workflows.updatestep method allows you to update the configuration of an existing workflow step in slack's workflow builder. this endpoint is essential for dynamically modifying workflow steps, enabling you to adjust inputs, outputs, and other properties of a step based on changing requirements or user interactions. it's particularly useful for maintaining and evolving complex workflows without rebuilding them from scratch. this method should be used when you need to programmatically update a workflow step's configuration, such as changing input fields, modifying output data structures, or updating the step's visual representation in the workflow builder. note that this endpoint is only available for org-ready apps with proper event subscriptions set up, and it replaces the deprecated "legacy steps from apps" feature.

**Action Parameters**

<ParamField path="inputs" type="string">
</ParamField>

<ParamField path="outputs" type="string">
</ParamField>

<ParamField path="step_image_url" type="string">
</ParamField>

<ParamField path="step_name" type="string">
</ParamField>

<ParamField path="workflow_step_edit_id" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_UPLOAD_OR_CREATE_A_FILE_IN_SLACK">
The files.upload endpoint allows you to upload files to slack channels. use this tool to share various content types, including documents and images, within slack conversations. it supports both direct content uploads and multipart form data, enabling text-based and binary file uploads. you can provide context like title, initial comment, and target channels. authentication with 'files:write:user' scope is required.

**Action Parameters**

<ParamField path="channels" type="string">
</ParamField>

<ParamField path="content" type="string">
</ParamField>

<ParamField path="file" type="string">
</ParamField>

<ParamField path="filename" type="string">
</ParamField>

<ParamField path="filetype" type="string">
</ParamField>

<ParamField path="initial_comment" type="string">
</ParamField>

<ParamField path="thread_ts" type="integer">
</ParamField>

<ParamField path="title" type="string">
</ParamField>

<ParamField path="token" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_USERS_INFO">
The users.info endpoint retrieves detailed information about a specific user in a slack workspace. it returns comprehensive profile data including the user's name, email, role, status, and other relevant details. this endpoint is particularly useful when you need to fetch up-to-date information about a user, such as checking their current status, verifying their role, or accessing their contact information. it should be used when detailed user data is required for user management, profile display, or integration with other systems. note that while this endpoint provides extensive user information, it does not return data about the user's message history or channel memberships.(DEPRECATED use retrieve_detailed_user_information)

**Action Parameters**

<ParamField path="include_locale" type="boolean">
</ParamField>

<ParamField path="user" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_USERS_LIST">
The 'users.list' endpoint retrieves a comprehensive list of users within a slack workspace. it provides detailed information about each user, including their profile data, account settings, and team memberships. this endpoint is particularly useful for applications that need to synchronize user data, manage user permissions, or perform bulk operations on user accounts. the response includes both active and deactivated users, allowing for a complete overview of the workspace's user base. however, it's important to note that the endpoint may not return real-time data, and there might be a slight delay in reflecting recent changes to user accounts.(DEPRECATED use list_all_slack_team_users_with_pagination)

**Action Parameters**

<ParamField path="cursor" type="string">
</ParamField>

<ParamField path="include_locale" type="boolean">
</ParamField>

<ParamField path="limit" type="integer" default="1">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_USERS_LOOKUP_BY_EMAIL">
The users.lookupbyemail endpoint retrieves detailed information about a single slack user by searching for their registered email address. this method is particularly useful when you need to find a user's slack profile details but only have their email address available. the endpoint returns a comprehensive user object containing various fields such as user id, display name, and other profile information if the user is active. however, it will return a 'users not found' error if the user has been deactivated or if no user with the specified email exists in the workspace. this method requires the 'users:read.email' scope for authentication and should be used judiciously to respect user privacy.(DEPRECATED use find_user_by_email_address)

**Action Parameters**

<ParamField path="email" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_USERS_PROFILE_GET_PROFILE_INFO">
The users.profile.get endpoint retrieves the profile information of a slack user. it allows you to fetch details such as display name, status, profile picture, and custom fields associated with a user's profile. this endpoint is particularly useful when you need to access up-to-date information about a specific user or the authenticated user. the retrieved data can be used for various purposes, such as populating user information in integrated applications or displaying user details in custom interfaces. note that while the endpoint provides comprehensive profile information, it does not include data about the user's participation in channels or their message history.(DEPRECATED use retrieve_user_profile_information)

**Action Parameters**

<ParamField path="include_labels" type="boolean">
</ParamField>

<ParamField path="user" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="SLACK_WIPE_USER_SESSIONS_ACROSS_DEVICES">
The admin.users.session.reset endpoint is used to invalidate all active sessions for a specified user in slack. this powerful administrative tool forces the user to log in again by wiping out their current sessions across all devices. it's particularly useful for security purposes, such as when a user's credentials may have been compromised, or when enforcing new security policies. the endpoint allows for selective session resets, targeting either mobile or web sessions, or both. it should be used cautiously as it can disrupt user access and ongoing work. this method requires administrative privileges and should only be employed when necessary for security or compliance reasons.

**Action Parameters**

<ParamField path="mobile_only" type="boolean">
</ParamField>

<ParamField path="user_id" type="string" required={true}>
</ParamField>

<ParamField path="web_only" type="boolean">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

</AccordionGroup>
