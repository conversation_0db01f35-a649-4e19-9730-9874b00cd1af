---
title: Gmail
subtitle: Learn how to use Gmail with Composio
---

## Overview

### Enum

`GMAIL`

### Description

Connect to Gmail to send and manage emails.

### Authentication Details

<Accordion title="OAUTH2">
<ParamField path="client_id" type="string" required={true}>
</ParamField>

<ParamField path="client_secret" type="string" required={true}>
</ParamField>

<ParamField path="oauth_redirect_uri" type="string" default="https://backend.composio.dev/api/v1/auth-apps/add">
</ParamField>

<ParamField path="scopes" type="string" default="https://www.googleapis.com/auth/gmail.modify,https://www.googleapis.com/auth/userinfo.profile">
</ParamField>

</Accordion>

<Accordion title="BEARER_TOKEN">
<ParamField path="token" type="string" required={true}>
</ParamField>

</Accordion>

## Actions

<AccordionGroup>
<Accordion title="GMAIL_ADD_LABEL_TO_EMAIL">
Modify a label to an email in gmail.

**Action Parameters**

<ParamField path="add_label_ids" type="array">
</ParamField>

<ParamField path="message_id" type="string" required={true}>
</ParamField>

<ParamField path="remove_label_ids" type="array">
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_CREATE_EMAIL_DRAFT">
Create a draft email using gmail's api.

**Action Parameters**

<ParamField path="attachment" type="object">
</ParamField>

<ParamField path="bcc" type="array">
</ParamField>

<ParamField path="body" type="string" required={true}>
</ParamField>

<ParamField path="cc" type="array">
</ParamField>

<ParamField path="extra_recipients" type="array">
</ParamField>

<ParamField path="is_html" type="boolean">
</ParamField>

<ParamField path="recipient_email" type="string" required={true}>
</ParamField>

<ParamField path="subject" type="string" required={true}>
</ParamField>

<ParamField path="thread_id" type="string">
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_CREATE_LABEL">
Action to create a new label in gmail.

**Action Parameters**

<ParamField path="label_list_visibility" type="string" default="labelShow">
</ParamField>

<ParamField path="label_name" type="string" required={true}>
</ParamField>

<ParamField path="message_list_visibility" type="string" default="show">
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_DELETE_DRAFT">
Delete an email draft using gmail's api.

**Action Parameters**

<ParamField path="draft_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_DELETE_MESSAGE">
Delete an email message using gmail's api. note: this action requires the integration to have the `https://mail.google.com/` scope.

**Action Parameters**

<ParamField path="message_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_FETCH_EMAILS">
Action to fetch all emails from gmail.

**Action Parameters**

<ParamField path="include_payload" type="boolean" default="True">
</ParamField>

<ParamField path="include_spam_trash" type="boolean">
</ParamField>

<ParamField path="label_ids" type="array">
</ParamField>

<ParamField path="max_results" type="integer" default="1">
</ParamField>

<ParamField path="page_token" type="string">
</ParamField>

<ParamField path="query" type="string">
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID">
Fetch messages by message id from gmail.

**Action Parameters**

<ParamField path="format" type="string" default="full">
</ParamField>

<ParamField path="message_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_FETCH_MESSAGE_BY_THREAD_ID">
Fetch messages by thread id from gmail with pagination support. to use pagination, you can set the 'pagetoken' in the request to the value of the 'nextpagetoken' in the response of the previous action. the 'nextpagetoken' is returned in the response of this action (i.e 'fetchmessagebythreadid') if there are more results to be fetched. if not provided, the first page of results is returned.

**Action Parameters**

<ParamField path="page_token" type="string">
</ParamField>

<ParamField path="thread_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_GET_ATTACHMENT">
Get an attachment from a mail.

**Action Parameters**

<ParamField path="attachment_id" type="string" required={true}>
</ParamField>

<ParamField path="file_name" type="string" required={true}>
</ParamField>

<ParamField path="message_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_GET_CONTACTS">
Action to get info of contacts saved in google for an authorized account. a custom integration with `https://www.googleapis.com/auth/contacts.readonly` scope is required to use this action.

**Action Parameters**

<ParamField path="page_token" type="string">
</ParamField>

<ParamField path="person_fields" type="string" default="emailAddresses,names,birthdays,genders">
</ParamField>

<ParamField path="resource_name" type="string" default="people/me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_GET_PEOPLE">
Action to get contacts info of people.

**Action Parameters**

<ParamField path="other_contacts" type="boolean">
</ParamField>

<ParamField path="page_size" type="integer" default="10">
</ParamField>

<ParamField path="page_token" type="string">
</ParamField>

<ParamField path="person_fields" type="string" default="emailAddresses,names,birthdays,genders">
</ParamField>

<ParamField path="resource_name" type="string" default="people/me">
</ParamField>

<ParamField path="sync_token" type="string">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_GET_PROFILE">
Get the profile of the authenticated user.

**Action Parameters**

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_LIST_DRAFTS">
List all email drafts using gmail's api.

**Action Parameters**

<ParamField path="max_results" type="integer" default="1">
</ParamField>

<ParamField path="page_token" type="string">
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_LIST_LABELS">
List all labels in the user's gmail account.

**Action Parameters**

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_LIST_THREADS">
Action to list threads in gmail. this action returns a list of threads and a page token to retrieve the next page of results. the next page token is returned in the response of this action (i.e 'listthreads') if there are more results to be fetched, which you can use in the 'pagetoken' field of the request to fetch the next page of results. if not available, the last page of results is returned.

**Action Parameters**

<ParamField path="max_results" type="integer" default="10">
</ParamField>

<ParamField path="page_token" type="string">
</ParamField>

<ParamField path="query" type="string">
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_MODIFY_THREAD_LABELS">
Action to modify labels of a thread in gmail.

**Action Parameters**

<ParamField path="add_label_ids" type="array">
</ParamField>

<ParamField path="remove_label_ids" type="array">
</ParamField>

<ParamField path="thread_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_MOVE_TO_TRASH">
Move an email message to trash using gmail's api.

**Action Parameters**

<ParamField path="message_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_REMOVE_LABEL">
Action to remove a label in gmail.

**Action Parameters**

<ParamField path="label_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_REPLY_TO_THREAD">
Action to reply to an email thread in gmail.

**Action Parameters**

<ParamField path="bcc" type="array">
</ParamField>

<ParamField path="cc" type="array">
</ParamField>

<ParamField path="extra_recipients" type="array">
</ParamField>

<ParamField path="is_html" type="boolean">
</ParamField>

<ParamField path="message_body" type="string" required={true}>
</ParamField>

<ParamField path="recipient_email" type="string" required={true}>
</ParamField>

<ParamField path="thread_id" type="string" required={true}>
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_SEARCH_PEOPLE">
Provides a list of contacts in the authenticated user's grouped contacts that matches the search query. the query matches on a contact's names, nicknames, emailaddresses, phonenumbers, and organizations fields that are from the contact source. a custom integration with `https://www.googleapis.com/auth/contacts.readonly` scope is required to use this action.

**Action Parameters**

<ParamField path="other_contacts" type="boolean">
</ParamField>

<ParamField path="pageSize" type="integer" default="10">
</ParamField>

<ParamField path="person_fields" type="string" default="emailAddresses,names,phoneNumbers">
</ParamField>

<ParamField path="query" type="string" required={true}>
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

<Accordion title="GMAIL_SEND_EMAIL">
Send an email using gmail's api.

**Action Parameters**

<ParamField path="attachment" type="object">
</ParamField>

<ParamField path="bcc" type="array">
</ParamField>

<ParamField path="body" type="string" required={true}>
</ParamField>

<ParamField path="cc" type="array">
</ParamField>

<ParamField path="extra_recipients" type="array">
</ParamField>

<ParamField path="is_html" type="boolean">
</ParamField>

<ParamField path="recipient_email" type="string" required={true}>
</ParamField>

<ParamField path="subject" type="string">
</ParamField>

<ParamField path="user_id" type="string" default="me">
</ParamField>

**Action Response**

<ParamField path="data" type="object">
</ParamField>

<ParamField path="error" type="">
</ParamField>

<ParamField path="successful" type="boolean">
</ParamField>

</Accordion>

</AccordionGroup>
